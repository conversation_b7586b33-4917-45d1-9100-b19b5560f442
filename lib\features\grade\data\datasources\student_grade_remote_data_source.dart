/// -----
/// student_grade_remote_data_source.dart
///
/// 学生成绩远程数据源接口，定义获取学生成绩数据的方法
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/grade/data/models/student_grade_model.dart';

/// 学生成绩远程数据源接口
///
/// 定义获取学生成绩数据的抽象方法
abstract class StudentGradeRemoteDataSource {
  /// 获取学生成绩列表
  ///
  /// [planId] 实习计划ID
  /// 
  /// 返回 [StudentGradeDataModel]
  Future<StudentGradeDataModel> getStudentGradeList(String planId);
}
