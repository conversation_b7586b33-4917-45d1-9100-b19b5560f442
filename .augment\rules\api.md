---
type: "agent_requested"
description: "api rules"
---
# API 规则

## 亿硕教育 API 规范
- 基础 URL: `http://**************:8089/userservice/userservice/`
- 响应格式: `{data, resultCode, resultMsg}`

## 请求格式规则
所有请求应使用 POST 方法，并包含以下头信息:
```json
{
  "Content-type": "application/json",
  "Accept": "application/json",
  "token": "Bearer {token}"
}
```

## 网络客户端配置规则
- 使用 `Dio` 作为HTTP客户端
- 在 `lib/core/network/api/dio_client.dart` 中实现 `DioClient` 类
- 配置基础URL、超时设置、默认头信息等
- 注册所有拦截器

### 必须注册的拦截器
1. **日志拦截器**: 记录请求和响应的详细信息
   - 在开发环境中启用，生产环境禁用
   
2. **认证拦截器**: 处理token的添加、刷新和过期逻辑
   - 在请求头中添加token
   - 处理token过期的情况，自动刷新token并重试请求

3. **错误处理拦截器**: 统一处理网络错误和服务器错误
   - 转换错误为应用内的异常类型

4. **缓存拦截器**: 实现请求缓存策略
   - 在无网络或指定情况下使用缓存数据

## 网络状态监控规则
- 实现 `NetworkInfo` 接口来检查网络连接状态
- 在请求前检查网络状态，避免无网络时发送请求

## 错误处理规则

### 异常类型
- `ServerException`: 服务器错误
- `CacheException`: 缓存错误
- `NetworkException`: 网络错误
- `UnauthorizedException`: 未授权错误
- `ForbiddenException`: 禁止访问错误
- `NotFoundException`: 资源不存在错误
- `RequestCancelledException`: 请求取消错误
- `UnknownException`: 未知错误

### 失败类型
- `ServerFailure`: 服务器失败
- `CacheFailure`: 缓存失败
- `NetworkFailure`: 网络失败

### 错误处理流程
1. 数据源层抛出异常
2. 仓库层捕获异常并转换为失败
3. 用例层返回 Either<Failure, T> 类型
4. BLoC 层处理失败并更新状态

## API 服务实现规则
- API 服务应实现在数据源层
- 使用 `DioClient` 发送网络请求
- 处理响应格式和错误情况
- 将响应数据转换为模型对象
