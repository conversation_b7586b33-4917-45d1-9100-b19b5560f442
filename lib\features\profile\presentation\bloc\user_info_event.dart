/// -----
/// user_info_event.dart
/// 
/// 用户信息BLoC事件类，定义用户信息相关的事件
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 用户信息事件基类
///
/// 所有用户信息相关事件的基类
/// 继承Equatable以便于比较和测试
abstract class UserInfoEvent extends Equatable {
  const UserInfoEvent();

  @override
  List<Object?> get props => [];
}

/// 获取用户信息事件
///
/// 触发获取用户信息的操作
/// 通常在页面初始化或需要刷新用户信息时触发
class GetUserInfoEvent extends UserInfoEvent {
  const GetUserInfoEvent();

  @override
  List<Object?> get props => [];
}

/// 刷新用户信息事件
///
/// 触发刷新用户信息的操作
/// 用于用户主动刷新或从其他页面返回时更新数据
class RefreshUserInfoEvent extends UserInfoEvent {
  const RefreshUserInfoEvent();

  @override
  List<Object?> get props => [];
}

/// 更新用户性别事件
///
/// 触发更新用户性别的操作
/// 用于用户在性别选择页面确认更新性别时
class UpdateUserGenderEvent extends UserInfoEvent {
  /// 性别（0女 1男 2未知）
  final int gender;

  const UpdateUserGenderEvent(this.gender);

  @override
  List<Object?> get props => [gender];
}

/// 保存用户头像事件
///
/// 触发保存用户头像的操作
/// 用于用户选择头像并上传成功后保存头像URL
class SaveUserAvatarEvent extends UserInfoEvent {
  /// 头像URL
  final String avatar;

  const SaveUserAvatarEvent(this.avatar);

  @override
  List<Object?> get props => [avatar];
}

/// 修改用户密码事件
///
/// 触发修改用户密码的操作
/// 用于用户在密码修改页面确认修改密码时
class ChangePasswordEvent extends UserInfoEvent {
  /// 旧密码
  final String oldPassword;

  /// 新密码
  final String newPassword;

  const ChangePasswordEvent({
    required this.oldPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [oldPassword, newPassword];
}

/// 重置用户信息事件
///
/// 触发重置用户信息状态的操作
/// 用于用户切换账号或认证失效时清空缓存的用户信息
class ResetUserInfoEvent extends UserInfoEvent {
  const ResetUserInfoEvent();

  @override
  List<Object?> get props => [];
}
