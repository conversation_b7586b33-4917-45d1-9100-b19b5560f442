/// 测试评分功能
///
/// 这个文件用于测试评分弹框和页面跳转功能
/// 运行命令: flutter run test_score_rating.dart

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/score_rating_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() {
  runApp(const TestScoreRatingApp());
}

class TestScoreRatingApp extends StatelessWidget {
  const TestScoreRatingApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(750, 1334),
      builder: (context, child) => MaterialApp(
        title: '评分弹框测试',
        theme: AppTheme.lightTheme,
        home: const TestScoreRatingScreen(),
      ),
    );
  }
}

class TestScoreRatingScreen extends StatefulWidget {
  const TestScoreRatingScreen({Key? key}) : super(key: key);

  @override
  State<TestScoreRatingScreen> createState() => _TestScoreRatingScreenState();
}

class _TestScoreRatingScreenState extends State<TestScoreRatingScreen> {
  int? _selectedScore;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('评分弹框测试'),
        backgroundColor: AppTheme.backgroundColor,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '当前选择的分数: ${_selectedScore ?? "未选择"}',
              style: TextStyle(
                fontSize: 32.sp,
                color: AppTheme.black333,
              ),
            ),
            SizedBox(height: 40.h),
            ElevatedButton(
              onPressed: _showScoreRatingDialog,
              child: Text(
                '打开评分弹框',
                style: TextStyle(fontSize: 28.sp),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showScoreRatingDialog() async {
    final score = await showScoreRatingDialog(
      context: context,
      title: '工作质量',
      weight: '20%',
      description: '任务完成准确性、专业度、细节处理能力',
      currentScore: _selectedScore,
      maxScore: 10,
    );

    if (score != null) {
      setState(() {
        _selectedScore = score;
      });
    }
  }
}
