/// -----
/// pdf_viewer_screen.dart
///
/// PDF查看器页面，用于预览PDF文件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

/// PDF查看器页面
class PdfViewerScreen extends StatefulWidget {
  /// PDF文件路径或URL
  final String pdfPath;
  
  /// 页面标题
  final String title;

  const PdfViewerScreen({
    Key? key,
    required this.pdfPath,
    this.title = 'PDF预览',
  }) : super(key: key);

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  /// PDF控制器
  late PdfViewerController _pdfViewerController;
  
  /// PDF加载错误信息
  String? _pdfError;
  
  /// 是否正在加载PDF
  bool _isLoadingPdf = true;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: widget.title,
        showBackButton: true,
      ),
      body: _buildPdfView(),
    );
  }

  /// 构建PDF视图
  Widget _buildPdfView() {
    final pdfPath = widget.pdfPath;

    // 如果路径为空，显示错误提示
    if (pdfPath.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80.w,
              color: Colors.red[300],
            ),
            SizedBox(height: 24.h),
            Text(
              'PDF文件路径为空',
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.w500,
                color: Colors.red[600],
              ),
            ),
          ],
        ),
      );
    }

    // 如果有错误信息，显示错误提示
    if (_pdfError != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80.w,
              color: Colors.red[300],
            ),
            SizedBox(height: 24.h),
            Text(
              'PDF加载失败',
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.w500,
                color: Colors.red[600],
              ),
            ),
            SizedBox(height: 16.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 32.w),
              child: Text(
                _pdfError!,
                style: TextStyle(
                  fontSize: 24.sp,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 32.h),
            ElevatedButton(
              onPressed: () {
                // 重新加载PDF
                setState(() {
                  _pdfError = null;
                  _isLoadingPdf = true;
                });
              },
              child: const Text('重新加载'),
            ),
          ],
        ),
      );
    }

    // 使用syncfusion_flutter_pdfviewer显示PDF
    return Container(
      margin: EdgeInsets.all(16.w),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: Stack(
          children: [
            SfPdfViewer.network(
              pdfPath,
              controller: _pdfViewerController,
              onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                // PDF文档加载完成，清除错误状态和加载状态
                setState(() {
                  _pdfError = null;
                  _isLoadingPdf = false;
                });
              },
              onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
                // PDF文档加载失败
                setState(() {
                  _pdfError = details.error;
                  _isLoadingPdf = false;
                });
              },
              // 启用文本选择
              enableTextSelection: true,
            ),

            // 加载指示器
            if (_isLoadingPdf)
              Container(
                color: Colors.white.withValues(alpha: 0.8),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 60.w,
                        height: 60.w,
                        child: const CircularProgressIndicator(
                          strokeWidth: 3,
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2165F6)),
                        ),
                      ),
                      SizedBox(height: 32.h),
                      Text(
                        '正在加载PDF文件...',
                        style: TextStyle(
                          fontSize: 28.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
