/// -----
/// internship_score_remote_data_source.dart
/// 
/// 实习成绩远程数据源接口和实现
///
/// <AUTHOR>
/// @date 2025-07-16
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/internship/data/models/internship_score_model.dart';
import 'package:flutter_demo/features/internship/data/models/score_item_model.dart';
import 'package:flutter_demo/features/internship/data/models/score_submit_request_model.dart';

/// 实习成绩远程数据源接口
abstract class InternshipScoreRemoteDataSource {
  /// 获取学生评分列表
  ///
  /// [planId] 计划ID
  /// [type] 评分类型，0:待评分，1:已评分
  Future<List<InternshipScoreModel>> getStudentScoreList({
    required String planId,
    required int type,
  });

  /// 获取评分项列表
  ///
  /// [planId] 计划ID
  Future<List<ScoreItemModel>> getScoreItems({
    required String planId,
  });

  /// 获取评分详情（已评分）
  ///
  /// [recordId] 评分记录ID
  Future<List<ScoreItemModel>> getScoreDetail({
    required String recordId,
  });

  /// 提交评分
  ///
  /// [request] 评分提交请求数据
  Future<void> submitScore({
    required ScoreSubmitRequestModel request,
  });
}

/// 实习成绩远程数据源实现
class InternshipScoreRemoteDataSourceImpl implements InternshipScoreRemoteDataSource {
  final DioClient _dioClient;

  const InternshipScoreRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<InternshipScoreModel>> getStudentScoreList({
    required String planId,
    required int type,
  }) async {
    try {
      // 调用API接口
      final response = await _dioClient.get(
        'internshipservice/v1/internship/evaluate/teacher/listStudent',
        queryParameters: {
          'planId': planId,
          'type': type,
        },
      );

      // 解析响应数据
      if (response is List) {
        return response
            .map((item) => InternshipScoreModel.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException('响应数据格式错误，期望List类型');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取学生评分列表失败: ${e.toString()}');
    }
  }

  @override
  Future<List<ScoreItemModel>> getScoreItems({
    required String planId,
  }) async {
    try {


      // 调用API接口
      final response = await _dioClient.get(
        'internshipservice/v1/internship/evaluate/teacher/listScoreItem',
        queryParameters: {
          'planId': planId,
        },
      );

      // 解析响应数据
      if (response is List) {
        return response
            .map((item) => ScoreItemModel.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException('响应数据格式错误，期望List类型');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取评分项列表失败: ${e.toString()}');
    }
  }

  @override
  Future<List<ScoreItemModel>> getScoreDetail({
    required String recordId,
  }) async {
    try {


      // 调用API接口
      final response = await _dioClient.get(
        'internshipservice/v1/internship/evaluate/scoreDetail',
        queryParameters: {
          'recordId': recordId,
        },
      );

      // 解析响应数据 - 根据接口文档，数据在data.item字段中
      if (response is Map<String, dynamic> && response['item'] is List) {
        final items = response['item'] as List;
        return items
            .map((item) => ScoreItemModel.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException('响应数据格式错误，期望包含item字段的Map类型');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取评分详情失败: ${e.toString()}');
    }
  }

  @override
  Future<void> submitScore({
    required ScoreSubmitRequestModel request,
  }) async {
    try {
      // 调用API接口
      await _dioClient.post(
        'internshipservice/v1/internship/evaluate/saveScore',
        data: request.toJson(),
      );
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('提交评分失败: ${e.toString()}');
    }
  }
}
