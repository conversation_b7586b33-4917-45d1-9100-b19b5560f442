/// -----
/// student_evaluate_score_request_model.dart
/// 
/// 学生评价老师评分请求模型
///
/// <AUTHOR>
/// @date 2025-07-19
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/internship/data/models/score_submit_request_model.dart';

/// 学生评价老师评分请求模型
///
/// 用于学生对老师进行评分的API请求
class StudentEvaluateScoreRequestModel {
  /// 评分项列表
  final List<ScoreSubmitItemModel> item;

  /// 实习计划ID
  final String planId;

  /// 评价类型：1:学生评价班主任老师，2:学生评价指导老师，3:学生评价企业老师
  final int type;

  const StudentEvaluateScoreRequestModel({
    required this.item,
    required this.planId,
    required this.type,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'item': item.map((e) => e.toJson()).toList(),
      'planId': planId,
      'type': type,
    };
  }

  /// 从JSON创建实例
  factory StudentEvaluateScoreRequestModel.fromJson(Map<String, dynamic> json) {
    return StudentEvaluateScoreRequestModel(
      item: (json['item'] as List<dynamic>)
          .map((e) {
            final itemMap = e as Map<String, dynamic>;
            return ScoreSubmitItemModel(
              content: itemMap['content'] as String,
              score: itemMap['score'] as int,
              title: itemMap['title'] as String,
            );
          })
          .toList(),
      planId: json['planId'] as String,
      type: json['type'] as int,
    );
  }

  /// 创建副本
  StudentEvaluateScoreRequestModel copyWith({
    List<ScoreSubmitItemModel>? item,
    String? planId,
    int? type,
  }) {
    return StudentEvaluateScoreRequestModel(
      item: item ?? this.item,
      planId: planId ?? this.planId,
      type: type ?? this.type,
    );
  }

  @override
  String toString() {
    return 'StudentEvaluateScoreRequestModel(item: $item, planId: $planId, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is StudentEvaluateScoreRequestModel &&
        other.item == item &&
        other.planId == planId &&
        other.type == type;
  }

  @override
  int get hashCode {
    return item.hashCode ^
        planId.hashCode ^
        type.hashCode;
  }
}
