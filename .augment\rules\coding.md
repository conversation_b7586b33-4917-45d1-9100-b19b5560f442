---
type: "agent_requested"
description: "Example description"
---
# 编码规则

## 命名约定
- **类名**: 使用 PascalCase (例如: `ContentRepository`)
- **变量和方法**: 使用 camelCase (例如: `getVideoList()`)
- **常量**: 使用 UPPER_SNAKE_CASE (例如: `MAX_RETRY_COUNT`)
- **私有成员**: 使用下划线前缀 (例如: `_apiClient`)
- **文件名**: 使用 snake_case (例如: `content_repository.dart`)

## 分层命名规范
- **实体**: 使用领域名称 (例如: `ContentItem`)
- **模型**: 使用领域名称加 Model 后缀 (例如: `ContentItemModel`)
- **仓库接口**: 使用领域名称加 Repository 后缀 (例如: `ContentRepository`)
- **仓库实现**: 使用领域名称加 RepositoryImpl 后缀 (例如: `ContentRepositoryImpl`)
- **数据源**: 使用领域名称加 DataSource 后缀 (例如: `ContentRemoteDataSource`)
- **用例**: 使用动词加领域名称 (例如: `GetContentDetail`)

## BLoC 模式规范
- 每个功能模块应有自己的 BLoC
- BLoC 应分为三个文件: `xxx_bloc.dart`, `xxx_event.dart`, `xxx_state.dart`
- 状态应该是不可变的(immutable)，继承自 Equatable
- 事件应该是明确的动作，继承自 Equatable
- BLoC 应该在 DI 容器中注册为工厂

## 导航规范
- 使用 go_router 进行导航
- 路由常量定义在 `lib/core/router/route_constants.dart` 中
- 路由配置定义在 `lib/core/router/app_router.dart` 中
- 使用 context.push() 或 context.go() 进行导航
- 使用路由抽象层简化导航操作

### 路由结构规范
- 路由常量使用 AppRoutes 类管理
- 路由参数键名使用 RouteParams 类管理
- 路由配置使用 AppRouter 类管理
- 路由导航使用 AppNavigator 抽象层

### 路由动画最佳实践
1. **区分不同类型的路由动画**:
   - 底部导航栏切换：使用 `NoTransitionPage` 避免动画
   - 普通页面跳转：使用标准的滑动或淡入淡出动画
   - 模态页面：使用从底部滑入的动画

2. **统一动画效果**:
   - 为所有页面跳转定义统一的动画效果
   - 避免不同页面间的动画不一致

3. **避免嵌套路由动画问题**:
   - 合理使用 ShellRoute 和 GoRoute
   - 避免过深的嵌套路由
