/// -----
/// baidu_location_service.dart
///
/// 百度定位服务
/// 封装百度地图定位相关的所有逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
import 'package:flutter_bmflocation/flutter_bmflocation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_demo/core/models/location_state.dart';

/// 百度定位服务
///
/// 单例模式，提供统一的定位服务接口
class BaiduLocationService {
  static BaiduLocationService? _instance;
  static BaiduLocationService get instance {
    if (_instance == null) {
      _instance = BaiduLocationService._();
      debugPrint('🆕 [定位服务] 创建新的定位服务实例');
    }
    return _instance!;
  }

  BaiduLocationService._();

  /// 定位状态流控制器
  final StreamController<LocationState> _locationController = 
      StreamController<LocationState>.broadcast();

  /// 定位状态流
  Stream<LocationState> get locationStream => _locationController.stream;

  /// 当前定位状态
  LocationState _currentState = const LocationState.initial();
  LocationState get currentState => _currentState;

  /// 百度定位插件
  LocationFlutterPlugin? _locationPlugin;

  /// 是否已初始化
  bool _isInitialized = false;

  /// 定位超时定时器
  Timer? _locationTimeoutTimer;

  /// 百度地图API Key
  static const String _apiKey = '6mPlvf905gybRsGzz2lZ3uvtuxZbAWd6';

  /// 初始化定位服务
  Future<void> initialize() async {
    debugPrint('🗺️ [定位服务] 开始初始化... 当前状态: _isInitialized=$_isInitialized');

    // 强制重置状态，确保每次都是全新的初始化
    await _forceReset();

    try {
      debugPrint('🗺️ [定位服务] 执行完整初始化流程...');
      _updateState(const LocationState.loading());

      // 1. 检查权限
      final hasPermission = await _checkAndRequestPermissions();
      if (!hasPermission) {
        _updateState(const LocationState.error(
          error: '定位权限被拒绝',
          location: '请在设置中开启定位权限',
        ));
        return;
      }

      // 2. 初始化百度地图SDK
      await _initializeBaiduSDK();

      // 3. 延迟500ms，确保SDK初始化完成
      await Future.delayed(const Duration(milliseconds: 500));

      // 4. 创建定位插件
      _locationPlugin = LocationFlutterPlugin();

      // 5. 设置隐私合规
      _setPrivacyCompliance();

      // 6. 再次延迟，确保隐私设置生效
      await Future.delayed(const Duration(milliseconds: 300));

      // 7. 配置定位参数
      await _configureLocationOptions();

      _isInitialized = true;
      debugPrint('✅ [定位服务] 初始化完成');

      // 8. 开始定位
      await startLocation();
    } catch (e) {
      debugPrint('❌ [定位服务] 初始化失败: $e');
      _updateState(LocationState.error(
        error: '定位服务初始化失败: $e',
        location: '无法初始化定位服务',
      ));
    }
  }

  /// 开始定位
  Future<void> startLocation() async {
    debugPrint('🚀 [定位服务] 开始定位... 初始化状态: $_isInitialized, 插件状态: ${_locationPlugin != null}');

    if (!_isInitialized || _locationPlugin == null) {
      debugPrint('❌ [定位服务] 服务未初始化，重新初始化');
      await initialize();
      return;
    }

    try {
      debugPrint('🚀 [定位服务] 执行定位...');
      _updateState(const LocationState.loading());

      // 取消之前的超时定时器
      _locationTimeoutTimer?.cancel();

      // 设置定位超时（30秒）
      _locationTimeoutTimer = Timer(const Duration(seconds: 20), () {
        debugPrint('⏰ [定位服务] 定位超时，重新尝试...');
        _updateState(const LocationState.error(
          error: '定位超时，请检查网络和GPS设置',
          location: '定位超时',
        ));
      });

      // 设置定位回调
      _setupLocationCallbacks();

      // 启动定位服务
      bool success = false;
      if (Platform.isAndroid) {
        debugPrint('🚀 [定位服务] 启动Android定位...');
        success = await _locationPlugin!.startLocation();
      } else if (Platform.isIOS) {
        debugPrint('🚀 [定位服务] 启动iOS定位...');
        success = await _locationPlugin!.singleLocation({
          'isReGeocode': true,
          'isNetworkState': true,
        });
      }

      debugPrint('🚀 [定位服务] 定位启动结果: $success');
      if (!success) {
        _locationTimeoutTimer?.cancel();
        _updateState(const LocationState.error(
          error: '定位服务启动失败',
          location: '无法启动定位',
        ));
      }
    } on Exception catch (e) {
      debugPrint('❌ [定位服务] 启动定位异常: $e');
      _locationTimeoutTimer?.cancel();

      // 特殊处理百度SDK的OkHttp兼容性问题
      String errorMessage = e.toString();
      if (errorMessage.contains('ExceptionInInitializerError') ||
          errorMessage.contains('Expected Android API level')) {
        debugPrint('🔧 [定位服务] 检测到SDK兼容性问题，尝试重新初始化...');
        // 重置服务状态，强制重新初始化
        await _forceReset();
        _updateState(const LocationState.error(
          error: 'SDK兼容性问题，请重试',
          location: '定位服务需要重新初始化',
        ));
      } else {
        _updateState(LocationState.error(
          error: '启动定位失败: $e',
          location: '定位启动异常',
        ));
      }
    } catch (e) {
      debugPrint('❌ [定位服务] 启动定位失败: $e');
      _locationTimeoutTimer?.cancel();
      _updateState(LocationState.error(
        error: '启动定位失败: $e',
        location: '定位启动异常',
      ));
    }
  }

  /// 停止定位
  Future<void> stopLocation() async {
    try {
      debugPrint('🛑 [定位服务] 停止定位...');
      await _locationPlugin?.stopLocation();
    } catch (e) {
      debugPrint('❌ [定位服务] 停止定位失败: $e');
    }
  }

  /// 刷新定位
  Future<void> refreshLocation() async {
    debugPrint('🔄 [定位服务] 刷新定位... 当前初始化状态: $_isInitialized');

    // 重新检查权限
    final hasPermission = await _checkAndRequestPermissions();
    if (!hasPermission) {
      _updateState(const LocationState.error(
        error: '定位权限被拒绝',
        location: '请在设置中开启定位权限',
      ));
      return;
    }

    // 关键修复：如果服务已初始化，先停止再重新开始，确保状态干净
    if (_isInitialized) {
      debugPrint('🔄 [定位服务] 服务已初始化，先停止再重新开始定位...');
      await stopLocation();
      // 短暂延迟确保停止完成
      await Future.delayed(const Duration(milliseconds: 200));
      await startLocation();
    } else {
      debugPrint('🔄 [定位服务] 服务未初始化，重新初始化...');
      await initialize();
    }
  }

  /// 强制重置服务状态（彻底清理）
  Future<void> _forceReset() async {
    debugPrint('🔄 [定位服务] 强制重置服务状态...');

    // 1. 取消所有定时器
    _locationTimeoutTimer?.cancel();

    // 2. 停止定位
    try {
      await _locationPlugin?.stopLocation();
    } catch (e) {
      debugPrint('⚠️ [定位服务] 停止定位时出错: $e');
    }

    // 3. 清理定位插件
    _locationPlugin = null;

    // 4. 重置初始化状态
    _isInitialized = false;



    // 6. 重置状态
    _updateState(const LocationState.initial());

    debugPrint('✅ [定位服务] 强制重置完成');
  }

  /// 重置服务状态（不销毁单例）
  void reset() {
    debugPrint('🔄 [定位服务] 重置服务状态...');
    _locationTimeoutTimer?.cancel();
    stopLocation();
    _isInitialized = false;

    _locationPlugin = null;
    _updateState(const LocationState.initial());
  }

  /// 完全重启定位服务（解决重复进入页面无法定位的问题）
  static Future<void> restart() async {
    debugPrint('🔄 [定位服务] 完全重启定位服务...');

    if (_instance != null) {
      // 强制销毁当前实例
      try {
        await _instance!._forceReset();
        _instance!._locationController.close();
      } catch (e) {
        debugPrint('⚠️ [定位服务] 销毁实例时出错: $e');
      }
      _instance = null;
    }

    // 等待一段时间确保资源释放
    await Future.delayed(const Duration(milliseconds: 500));

    debugPrint('✅ [定位服务] 重启完成，下次获取instance将创建新实例');
  }

  /// 销毁服务（仅在应用退出时调用）
  void dispose() {
    debugPrint('🗑️ [定位服务] 销毁服务...');
    _locationTimeoutTimer?.cancel();
    stopLocation();
    _locationController.close();
    _isInitialized = false;
    _instance = null;
  }

  /// 检查并请求权限
  Future<bool> _checkAndRequestPermissions() async {
    debugPrint('🔐 [定位服务] 检查定位权限...');

    // 检查当前权限状态
    final locationPermission = await Permission.location.status;
    final locationWhenInUsePermission = await Permission.locationWhenInUse.status;

    if (locationPermission.isGranted || locationWhenInUsePermission.isGranted) {
      debugPrint('✅ [定位服务] 定位权限已授权');
      return true;
    }

    if (locationPermission.isPermanentlyDenied || 
        locationWhenInUsePermission.isPermanentlyDenied) {
      debugPrint('❌ [定位服务] 定位权限被永久拒绝');
      return false;
    }

    // 请求权限
    debugPrint('🔐 [定位服务] 请求定位权限...');
    final results = await [
      Permission.location,
      Permission.locationWhenInUse,
    ].request();

    final granted = results[Permission.location]?.isGranted == true ||
                   results[Permission.locationWhenInUse]?.isGranted == true;

    debugPrint(granted ? '✅ [定位服务] 权限授权成功' : '❌ [定位服务] 权限授权失败');
    return granted;
  }

  /// 初始化百度SDK
  Future<void> _initializeBaiduSDK() async {
    debugPrint('🗺️ [定位服务] 初始化百度地图SDK...');

    try {
      // 重新设置API Key和坐标系
      BMFMapSDK.setApiKeyAndCoordType(_apiKey, BMF_COORD_TYPE.BD09LL);
      debugPrint('✅ [定位服务] 百度地图SDK设置完成');
    } catch (e) {
      debugPrint('❌ [定位服务] 百度地图SDK设置失败: $e');
      throw Exception('百度地图SDK初始化失败: $e');
    }
  }

  /// 设置隐私合规
  void _setPrivacyCompliance() {
    debugPrint('🔒 [定位服务] 设置隐私合规...');
    BMFMapSDK.setAgreePrivacy(true);
    _locationPlugin?.setAgreePrivacy(true);
  }

  /// 配置定位参数
  Future<void> _configureLocationOptions() async {
    if (_locationPlugin == null) return;

    debugPrint('⚙️ [定位服务] 配置定位参数...');

    // Android定位参数
    final androidOptions = BaiduLocationAndroidOption(
      locationMode: BMFLocationMode.hightAccuracy,
      isNeedAddress: true,
      isNeedAltitude: true,
      isNeedLocationPoiList: true,
      isNeedNewVersionRgc: true,
      isNeedLocationDescribe: true,
      openGps: true,
      locationPurpose: BMFLocationPurpose.signIn,
      coordType: BMFLocationCoordType.bd09ll,
      scanspan: 0,
    );

    // iOS定位参数
    final iosOptions = BaiduLocationIOSOption(
      coordType: BMFLocationCoordType.bd09ll,
      locationTimeout: 10,
      reGeocodeTimeout: 10,
      activityType: BMFActivityType.automotiveNavigation,
      desiredAccuracy: BMFDesiredAccuracy.best,
      isNeedNewVersionRgc: true,
      pausesLocationUpdatesAutomatically: false,
      distanceFilter: 10,
    );

    final success = await _locationPlugin!.prepareLoc(
      androidOptions.getMap(),
      iosOptions.getMap(),
    );

    if (!success) {
      throw Exception('定位参数配置失败');
    }
  }

  /// 设置定位回调
  void _setupLocationCallbacks() {
    if (_locationPlugin == null) return;

    debugPrint('📡 [定位服务] 设置定位回调...');

    try {
      if (Platform.isAndroid) {
        debugPrint('📡 [定位服务] 设置Android回调...');
        _locationPlugin!.seriesLocationCallback(callback: _handleLocationResult);
      }

      if (Platform.isIOS) {
        debugPrint('📡 [定位服务] 设置iOS回调...');
        _locationPlugin!.singleLocationCallback(callback: _handleLocationResult);
      }
    } on Exception catch (e) {
      debugPrint('❌ [定位服务] 设置回调失败: $e');
    }
  }

  /// 处理定位结果
  void _handleLocationResult(BaiduLocation result) {
    debugPrint('📍 [定位服务] 收到定位结果');
    debugPrint('📍 [定位服务] 经度: ${result.longitude}, 纬度: ${result.latitude}');
    debugPrint('📍 [定位服务] 地址: ${result.address}');

    // 取消超时定时器
    _locationTimeoutTimer?.cancel();

    if (result.latitude != null && result.longitude != null) {
      // 构建地址信息
      String address = '';
      if (result.address != null && result.address!.isNotEmpty) {
        address = result.address!;
      } else {
        List<String> addressParts = [];
        if (result.province != null) addressParts.add(result.province!);
        if (result.city != null) addressParts.add(result.city!);
        if (result.district != null) addressParts.add(result.district!);
        if (result.street != null) addressParts.add(result.street!);
        address = addressParts.join('');
      }

      if (address.isEmpty) address = '位置信息获取中...';

      _updateState(LocationState.success(
        location: address,
        latitude: result.latitude.toString(),
        longitude: result.longitude.toString(),
        province: result.province ?? '',
        city: result.city ?? '',
      ));

      debugPrint('✅ [定位服务] 定位成功: $address');
    } else {
      _updateState(const LocationState.error(
        error: '定位失败，经纬度为空',
        location: '定位失败，请检查网络连接',
      ));
    }
  }

  /// 更新状态
  void _updateState(LocationState newState) {
    _currentState = newState;
    _locationController.add(newState);
  }
}
