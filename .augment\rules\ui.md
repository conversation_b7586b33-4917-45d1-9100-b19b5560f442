---
type: "agent_requested"
description: "Example description"
---
# UI 规则

## 应用栏规则
- **统一使用自定义AppBar**: 需要的页面使用 `CustomAppBar` 组件，而不是标准的 `AppBar`
- **实现位置**: 该组件位于 `lib/core/widgets/custom_app_bar.dart`
- **一致性**: 确保整个应用的导航栏样式一致
- **自定义选项**: 支持标题、返回按钮、操作按钮、背景颜色等自定义选项

## Loading 页面规则
- **统一样式**: 所有 Loading 页面应使用统一的样式和动画
- **实现方式**: 使用 `LoadingWidget` 组件，该组件位于 `lib/core/widgets/loading_widget.dart`
- **状态管理**: 在 BLoC 中使用 `LoadingState` 状态来控制 Loading 的显示和隐藏
- **超时处理**: Loading 状态应设置超时处理，默认 30 秒后转为错误状态
- **取消操作**: 提供取消加载的选项，特别是在耗时操作中

## 缺省页规则
- **类型划分**: 缺省页应根据不同情况划分为以下几种类型:
  - **空数据缺省页**: 当没有数据时显示
  - **网络错误缺省页**: 当网络连接失败时显示
  - **服务器错误缺省页**: 当服务器返回错误时显示
  - **权限错误缺省页**: 当用户没有权限访问内容时显示
  - **搜索无结果缺省页**: 当搜索没有结果时显示
- **实现方式**: 使用 `EmptyStateWidget` 组件，该组件位于 `lib/core/widgets/empty_state_widget.dart`
- **交互设计**: 缺省页应提供明确的用户操作指引，如"重试"、"返回"等按钮
- **状态管理**: 在 BLoC 中使用对应的状态（如 `EmptyState`、`ErrorState` 等）来控制缺省页的显示

## 提示（Snackbar）规则
- **统一使用AppSnackBar**: 应用中所有提示信息应使用 `AppSnackBar` 工具类，而不是直接使用 `ScaffoldMessenger.showSnackBar`
- **实现位置**: 该工具类位于 `lib/core/widgets/app_snack_bar.dart`
- **提示类型**: 根据不同场景使用不同类型的提示:
  - **普通提示**: 使用 `AppSnackBar.show()` 方法，默认样式
  - **成功提示**: 使用 `AppSnackBar.showSuccess()` 方法，绿色背景
  - **错误提示**: 使用 `AppSnackBar.showError()` 方法，红色背景
  - **警告提示**: 使用 `AppSnackBar.showWarning()` 方法，橙色背景
  - **带操作按钮的提示**: 使用 `AppSnackBar.showWithAction()` 方法，包含可点击的操作按钮
- **显示时长**: 根据提示类型和内容长度设置合适的显示时长
  - 普通提示: 默认2秒
  - 成功提示: 默认2秒
  - 错误提示: 默认3秒
  - 警告提示: 默认3秒
  - 带操作按钮的提示: 默认4秒
- **样式规范**:
  - 使用圆角边框（8.r）
  - 使用浮动行为（SnackBarBehavior.floating）
  - 四周边距为16.w
  - 文字大小为28.sp
  - 文字颜色为白色

## 使用场景指南
- **表单提交成功**: 使用成功提示，简短描述操作结果
- **操作错误**: 使用错误提示，说明错误原因和可能的解决方法
- **需要用户确认的操作**: 使用带操作按钮的提示，提供撤销或确认选项
- **网络状态变化**: 使用警告提示，告知用户网络状态
- **BLoC状态监听**: 在BLoC的listener中根据状态显示相应提示
