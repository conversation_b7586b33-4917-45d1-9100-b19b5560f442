/// -----
/// user_info_repository_impl.dart
/// 
/// 用户信息仓库实现，封装数据源调用和错误处理
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/profile/data/datasources/remote/user_info_remote_data_source.dart';
import 'package:flutter_demo/features/profile/domain/entities/user_info.dart';
import 'package:flutter_demo/features/profile/domain/repositories/user_info_repository.dart';

/// 用户信息仓库实现
///
/// 实现用户信息仓库接口，封装数据源调用和错误处理
/// 将异常转换为Failure对象，符合Clean Architecture规范
class UserInfoRepositoryImpl implements UserInfoRepository {
  final UserInfoRemoteDataSource _remoteDataSource;
  
  static const String _tag = 'UserInfoRepository';

  UserInfoRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, UserInfo>> getUserInfo() async {
    try {
      Logger.info(_tag, '开始获取用户信息');
      
      // 调用远程数据源获取用户信息
      final userInfo = await _remoteDataSource.getUserInfo();
      
      Logger.info(_tag, '用户信息获取成功');
      return Right(userInfo);
    } on ServerException catch (e) {
      Logger.error(_tag, '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error(_tag, '网络异常: ${e.message}');
      return Left(NetworkFailure(e.message));
    } catch (e) {
      Logger.error(_tag, '未知异常: $e');
      return Left(ServerFailure('获取用户信息失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> updateUserGender(int gender) async {
    try {
      Logger.info(_tag, '开始更新用户性别: $gender');

      // 调用远程数据源更新用户性别
      final result = await _remoteDataSource.updateUserGender(gender);

      Logger.info(_tag, '用户性别更新成功');
      return Right(result);
    } on ServerException catch (e) {
      Logger.error(_tag, '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error(_tag, '网络异常: ${e.message}');
      return Left(NetworkFailure(e.message));
    } catch (e) {
      Logger.error(_tag, '未知异常: $e');
      return Left(ServerFailure('更新用户性别失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> saveUserAvatar(String avatar) async {
    try {
      Logger.info(_tag, '开始保存用户头像: $avatar');

      // 调用远程数据源保存用户头像
      final result = await _remoteDataSource.saveUserAvatar(avatar);

      Logger.info(_tag, '用户头像保存成功');
      return Right(result);
    } on ServerException catch (e) {
      Logger.error(_tag, '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error(_tag, '网络异常: ${e.message}');
      return Left(NetworkFailure(e.message));
    } catch (e) {
      Logger.error(_tag, '未知异常: $e');
      return Left(ServerFailure('保存用户头像失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> changePassword(String oldPassword, String newPassword) async {
    try {
      Logger.info(_tag, '开始修改用户密码');

      // 调用远程数据源修改用户密码
      final result = await _remoteDataSource.changePassword(oldPassword, newPassword);

      Logger.info(_tag, '用户密码修改成功');
      return Right(result);
    } on ServerException catch (e) {
      Logger.error(_tag, '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error(_tag, '网络异常: ${e.message}');
      return Left(NetworkFailure(e.message));
    } catch (e) {
      Logger.error(_tag, '未知异常: $e');
      return Left(ServerFailure('修改用户密码失败: $e'));
    }
  }
}
