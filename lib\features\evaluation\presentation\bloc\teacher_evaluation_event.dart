/// -----
/// teacher_evaluation_event.dart
/// 
/// 老师评价事件
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

part of 'teacher_evaluation_bloc.dart';

/// 老师评价事件基类
abstract class TeacherEvaluationEvent extends Equatable {
  const TeacherEvaluationEvent();

  @override
  List<Object?> get props => [];
}

/// 加载老师列表事件
class LoadTeacherListEvent extends TeacherEvaluationEvent {
  /// 实习计划ID
  final String planId;

  const LoadTeacherListEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}
