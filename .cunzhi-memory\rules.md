# 开发规范和规则

- 实习评分详情页面必须使用全局实习计划状态的currentPlanId作为接口的planId参数，而不是使用传入的courseId，以确保调用正确的实习计划数据，避免"实习计划不存在"错误
- 在数据模型的fromJson方法中，应该使用?.toString()进行类型转换而不是直接as转换，以避免接口返回的数据类型与期望类型不匹配导致的运行时错误，特别是当接口返回double但代码期望String时
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行
- 文件上传详情页面已修改为动态获取planId：在file_upload_detail_screen.dart中添加了_getCurrentPlanId()方法，优先从PlanListGlobalBloc全局状态获取currentPlanId，其次从本地存储获取current_plan_id，最后使用默认值8。StartUploadFileEvent调用时使用动态获取的planId而不是硬编码的_uploadItem.planId，确保v1/internship/student/file/save接口使用正确的当前实习计划ID。
- 文件上传详情页面已修改为使用应用内PDF查看器：在file_upload_detail_screen.dart中修改了_openPDFFile方法，移除了url_launcher依赖，改为使用PdfViewerScreen组件在应用内预览PDF文件，解决了"无法打开文件，请检查文件链接"的问题。点击已上传的PDF文件时，会使用Navigator.push跳转到PdfViewerScreen页面进行预览。
- 修复了实习申请审批"通过"按钮提示"审批状态无效"的问题：在approve_internship_application_usecase.dart中，状态验证逻辑错误地使用了文件审批的状态值（2和3），但实习申请审批的正确状态值是1=通过，2=驳回。已将验证条件从status != 2 && status != 3修改为status != 1 && status != 2，确保"通过"按钮（status=1）能正常工作。
- 修复了文件上传详情页面审批信息显示问题：在file_upload_detail_screen.dart中修改了_buildApprovalSection方法，将硬编码的"冯项老师 (班主任)"替换为从API接口返回的动态数据。现在使用FileDetailModel中的approveName和approveRoleName字段显示真实的审批人信息，当没有审批人信息时隐藏审批区域，并根据fileStatus字段动态显示审批状态（待审批、审核通过、已驳回等）。
- 修改了文件上传详情页面审批区域显示逻辑：移除了当审批人信息为空时隐藏审批区域的条件判断，现在审批区域会始终显示。当API返回的approveName和approveRoleName都为空时，会显示默认文本"审批人"，确保用户始终能看到审批状态信息。
