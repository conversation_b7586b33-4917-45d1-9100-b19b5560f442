/// -----
/// get_my_insurance_usecase.dart
/// 
/// 获取我的实习保险信息用例
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import '../entities/insurance_info.dart';
import '../repositories/insurance_repository.dart';

/// 获取我的实习保险信息用例
class GetMyInsuranceUseCase implements UseCase<List<InsuranceInfo>, GetMyInsuranceParams> {
  final InsuranceRepository repository;

  const GetMyInsuranceUseCase(this.repository);

  @override
  Future<Either<Failure, List<InsuranceInfo>>> call(GetMyInsuranceParams params) async {
    return await repository.getMyInsurance(planId: params.planId);
  }
}

/// 获取我的实习保险信息参数
class GetMyInsuranceParams extends Equatable {
  /// 实习计划ID
  final String planId;

  const GetMyInsuranceParams({required this.planId});

  @override
  List<Object> get props => [planId];
}
