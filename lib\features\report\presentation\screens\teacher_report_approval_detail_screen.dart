/// -----
/// teacher_report_approval_detail_screen.dart
///
/// 教师端报告审核详情页面，用于展示学生提交的报告详情并进行批阅
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/report_detail_model.dart';
import 'package:flutter_demo/features/report/presentation/bloc/detail/report_detail_bloc.dart';
import 'package:flutter_demo/features/report/presentation/bloc/detail/report_detail_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/detail/report_detail_state.dart';
import 'package:flutter_demo/features/report/presentation/widgets/student_info_card.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class TeacherReportApprovalDetailScreen extends StatefulWidget {
  final String reportId;

  const TeacherReportApprovalDetailScreen({
    Key? key,
    required this.reportId,
  }) : super(key: key);

  @override
  State<TeacherReportApprovalDetailScreen> createState() =>
      _TeacherReportApprovalDetailScreenState();
}

class _TeacherReportApprovalDetailScreenState
    extends State<TeacherReportApprovalDetailScreen> {
  // 评分星级（可变）
  double _rating = 0.0;

  // 评阅意见输入控制器
  final TextEditingController _reviewCommentsController = TextEditingController();

  @override
  void dispose() {
    _reviewCommentsController.dispose();
    super.dispose();
  }

  // 提交评阅
  Future<void> _submitReview() async {
    final reviewComments = _reviewCommentsController.text.trim();

    // 检查是否输入了评阅意见
    if (reviewComments.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入评阅意见'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      // 直接使用widget.reportId，因为我们已经知道报告ID
      // 将reportId转换为int类型
      final reportId = int.tryParse(widget.reportId);
      if (reportId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('报告ID格式错误'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // 调用API提交评阅意见
      await _submitReviewToApi(
        reportId: reportId,
        comments: reviewComments,
      );

      // 提交成功
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('评阅提交成功（评分：${_rating.toInt()}星）'),
            backgroundColor: Colors.green,
          ),
        );

        // 延迟一下让用户看到成功消息，然后返回上一页并刷新
        await Future.delayed(const Duration(milliseconds: 1500));

        if (mounted) {
          // 返回上一页，并传递刷新标识
          Navigator.of(context).pop(true); // 传递true表示需要刷新
        }
      }
    } on ServerException catch (e) {
      // 提交失败
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('评阅提交失败：${e.message}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } on Exception catch (e) {
      // 其他异常
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('评阅提交失败：${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 调用API提交评阅意见
  Future<void> _submitReviewToApi({
    required int reportId,
    required String comments,
  }) async {
    final dioClient = GetIt.instance<DioClient>();

    try {
      await dioClient.post(
        'internshipservice/v1/internship/report/teacher/review',
        data: {
          'id': reportId,
          'comments': comments,
        },
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException('提交评阅失败: ${e.toString()}');
    }
  }



  // 获取报告周期信息
  String _getReportPeriodInfo(ReportDetailModel reportDetail) {
    switch (reportDetail.reportType) {
      case 1: // 日报
        return '日报 ${DateFormat('yyyy.MM.dd').format(reportDetail.reportDateTime)}';
      case 2: // 周报
        if (reportDetail.reportStartDateTime != null && reportDetail.reportEndDateTime != null) {
          return '周报 ${DateFormat('yyyy.MM.dd').format(reportDetail.reportStartDateTime!)}-${DateFormat('yyyy.MM.dd').format(reportDetail.reportEndDateTime!)}';
        } else {
          return '周报 ${DateFormat('yyyy.MM.dd').format(reportDetail.reportDateTime)}';
        }
      case 3: // 月报
        if (reportDetail.reportStartDateTime != null && reportDetail.reportEndDateTime != null) {
          return '月报 ${DateFormat('yyyy.MM.dd').format(reportDetail.reportStartDateTime!)}-${DateFormat('yyyy.MM.dd').format(reportDetail.reportEndDateTime!)}';
        } else {
          return '月报 ${DateFormat('yyyy.MM.dd').format(reportDetail.reportDateTime)}';
        }
      case 4: // 总结
        if (reportDetail.reportStartDateTime != null && reportDetail.reportEndDateTime != null) {
          return '实习总结 ${DateFormat('yyyy.MM.dd').format(reportDetail.reportStartDateTime!)}-${DateFormat('yyyy.MM.dd').format(reportDetail.reportEndDateTime!)}';
        } else {
          return '实习总结 ${DateFormat('yyyy.MM.dd').format(reportDetail.reportDateTime)}';
        }
      default:
        return '报告 ${DateFormat('yyyy.MM.dd').format(reportDetail.reportDateTime)}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<ReportDetailBloc>()
        ..add(GetReportDetailEvent(reportId: widget.reportId)),
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: CustomAppBar(
          title: '学生报告详情',
          backgroundColor: Colors.white,
          showBackButton: true,
        ),
        // 使用bottomNavigationBar确保按钮固定在底部
        bottomNavigationBar: BlocBuilder<ReportDetailBloc, ReportDetailState>(
          builder: (context, state) {
            // 只有在数据加载成功且状态为待批阅时才显示提交按钮
            if (state is ReportDetailLoaded && state.reportDetail.status == 0) {
              return _buildBottomButton();
            }
            return const SizedBox.shrink(); // 其他状态下隐藏按钮
          },
        ),
        body: BlocBuilder<ReportDetailBloc, ReportDetailState>(
          builder: (context, state) {
            if (state is ReportDetailLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else if (state is ReportDetailError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '加载失败',
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      state.message,
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 32.h),
                    ElevatedButton(
                      onPressed: () {
                        context.read<ReportDetailBloc>().add(
                          GetReportDetailEvent(reportId: widget.reportId),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                      ),
                      child: Text(
                        '重试',
                        style: TextStyle(
                          fontSize: 26.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            } else if (state is ReportDetailLoaded) {
              return _buildContent(state.reportDetail);
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  // 构建内容区域
  Widget _buildContent(ReportDetailModel reportDetail) {
    return Column(
      children: [
        // 课程头部 - 使用普通文本显示
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
          color: Colors.white,
          child: _buildCourseTitle(reportDetail), // 从接口数据获取课程标题
        ),

        // 内容区域
        Expanded(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 学生信息卡片
                        _buildStudentInfoCard(reportDetail),

                        // 报告内容区域
                        _buildReportContentCard(reportDetail),

                        // 底部安全间距
                        SizedBox(height: 16.h),
                      ],
                    ),
                  ),
                  SizedBox(height: 20.h),
                  // AI评阅区域
                  _buildAIReviewSection(reportDetail),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建学生信息卡片
  Widget _buildStudentInfoCard(ReportDetailModel reportDetail) {
    return StudentInfoCard(
      userName: _getStudentName(reportDetail), // 从接口数据获取学生信息
      company: _getCompanyName(reportDetail),
      status: reportDetail.status == 1 ? ReportStatus.approved : ReportStatus.submitted,
      position: _getStudentPosition(reportDetail),
      margin: EdgeInsets.zero,
      padding: EdgeInsets.all(16.r),
      avatarRadius: 88.r,
    );
  }

  // 获取学生姓名
  String _getStudentName(ReportDetailModel reportDetail) {
    // 由于接口暂时只返回studentId，这里使用学生ID作为显示名称
    // 后续可以通过studentId调用学生详情接口获取真实姓名
    if (reportDetail.studentId.isNotEmpty) {
      // 尝试从studentId中提取有意义的信息
      try {
        final studentIdInt = int.parse(reportDetail.studentId);
        return '学生$studentIdInt';
      } on FormatException {
        return '学生${reportDetail.studentId}';
      }
    }
    return '未知学生';
  }

  // 获取公司名称
  String _getCompanyName(ReportDetailModel reportDetail) {
    // 优先使用指导老师信息推断公司
    if (reportDetail.companyReviewPerson != null &&
        reportDetail.companyReviewPerson!.isNotEmpty) {
      return '${reportDetail.companyReviewPerson}所在单位';
    }

    // 如果有审核意见，说明有实习单位
    if (reportDetail.companyReviewComments != null &&
        reportDetail.companyReviewComments!.isNotEmpty) {
      return '实习单位';
    }

    return '暂无实习单位';
  }

  // 获取学生职位
  String _getStudentPosition(ReportDetailModel reportDetail) {
    // 根据报告类型推断职位信息
    switch (reportDetail.reportType) {
      case 1: // 日报
      case 2: // 周报
      case 3: // 月报
        return '实习生'; // 通用职位
      case 4: // 总结
        return '实习生';
      default:
        return '实习生';
    }
  }

  // 构建课程标题Widget
  Widget _buildCourseTitle(ReportDetailModel reportDetail) {
    return BlocBuilder<PlanListGlobalBloc, PlanListGlobalState>(
      bloc: GetIt.instance<PlanListGlobalBloc>(),
      builder: (context, state) {
        String courseTitle = _getDefaultCourseTitle(reportDetail);

        // 如果全局状态已加载，尝试通过planId获取实习计划名称
        if (state is PlanListGlobalLoadedState) {
          final planIdStr = reportDetail.planId.toString();
          final plan = state.plans.where((plan) => plan.planId == planIdStr).firstOrNull;
          if (plan != null) {
            courseTitle = plan.planName;
          } else {
            // 如果找不到对应的计划，使用默认标题
            courseTitle = _getDefaultCourseTitle(reportDetail);
          }
        }

        return Text(
          courseTitle,
          style: TextStyle(
            fontSize: 28.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        );
      },
    );
  }

  // 获取默认课程标题（根据报告类型）
  String _getDefaultCourseTitle(ReportDetailModel reportDetail) {
    switch (reportDetail.reportType) {
      case 1:
        return '实习日报';
      case 2:
        return '实习周报';
      case 3:
        return '实习月报';
      case 4:
        return '实习总结';
      default:
        return '实习报告';
    }
  }

  // 构建评阅意见输入框
  Widget _buildReviewCommentsInput(ReportDetailModel reportDetail) {
    // 初始化输入框内容
    final initialText = _getInitialReviewComments(reportDetail);
    if (_reviewCommentsController.text != initialText) {
      _reviewCommentsController.text = initialText;
    }

    // 根据报告状态决定是否可编辑
    final isEditable = reportDetail.status == 0; // 0=待批阅，1=已批阅

    return SizedBox(
      width: double.infinity, // 确保输入框占满容器宽度
      child: TextField(
        controller: _reviewCommentsController,
        readOnly: !isEditable, // 已批阅状态下设为只读
        maxLines: null, // 允许多行输入
        minLines: 5, // 增加最少显示行数，让输入框更大
        decoration: InputDecoration(
          border: InputBorder.none, // 去掉边框，因为外层容器已有背景
          hintText: isEditable ? '请输入评阅意见...' : '已批阅，不可编辑',
          contentPadding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 32.h), // 添加内边距
          isDense: false, // 确保输入框不会被压缩
        ),
        style: TextStyle(
          fontSize: 26.sp,
          color: isEditable ? Colors.black87 : Colors.grey[600], // 只读时使用灰色
          height: 1.5,
        ),
      ),
    );
  }

  // 构建可交互的星星评分组件
  Widget _buildInteractiveRating(ReportDetailModel reportDetail) {
    // 根据报告状态决定是否可交互
    final isEditable = reportDetail.status == 0; // 0=待批阅，1=已批阅

    return Row(
      children: List.generate(5, (index) {
        final starIndex = index + 1; // 星星从1开始计数
        final isFilled = starIndex <= _rating;

        return GestureDetector(
          onTap: isEditable ? () {
            setState(() {
              _rating = starIndex.toDouble();
            });
          } : null,
          child: Padding(
            padding: EdgeInsets.only(right: index < 4 ? 40.w : 0),
            child: Icon(
              isFilled ? Icons.star : Icons.star_border,
              color: isEditable ? Colors.amber : Colors.grey[400],
              size: 40.r,
            ),
          ),
        );
      }),
    );
  }

  // 获取初始评阅意见
  String _getInitialReviewComments(ReportDetailModel reportDetail) {
    // 使用接口返回的reviewComments字段作为默认老师评语
    if (reportDetail.reviewComments != null &&
        reportDetail.reviewComments!.trim().isNotEmpty) {
      return reportDetail.reviewComments!.trim();
    }

    // 如果reviewComments为空，默认为空让用户输入
    // 不再使用companyReviewComments，因为那是指导老师的意见，不是当前老师的评语
    return '';
  }

  // 获取报告内容
  String _getReportContent(ReportDetailModel reportDetail) {
    if (reportDetail.reportContent.trim().isNotEmpty) {
      return reportDetail.reportContent.trim();
    }
    return '暂无报告内容';
  }

  // 构建报告内容区域
  Widget _buildReportContentCard(ReportDetailModel reportDetail) {
    return Padding(
      padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 报告标题
          Text(
            _getReportPeriodInfo(reportDetail),
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 39.h),

          // 报告内容
          _buildQuestionAnswer(
              '报告内容',
              _getReportContent(reportDetail)
          ),
          SizedBox(height: 24.h),
          // 提交时间
          Text(
            '提交时间：${DateFormat('yyyy-MM-dd HH:mm').format(reportDetail.createDateTime)}',
            style: TextStyle(
              fontSize: 24.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  // 构建问题和回答
  Widget _buildQuestionAnswer(String question, String answer) {
    return // 回答内容 - 使用灰色背景
      Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 28.h),
        decoration: BoxDecoration(
          color: const Color(0xFFF7F7F7),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 问题文本
            Text(
              question,
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.black333,
                height: 1.4,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              answer,
              style: TextStyle(
                fontSize: 24.sp,
                color: AppTheme.black333,
                height: 1.4,
              ),
            )
          ],
        ),
      );
  }

  // 构建AI评阅区域
  Widget _buildAIReviewSection(ReportDetailModel reportDetail) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        image: const DecorationImage(
          image: AssetImage('assets/images/ai_review_dialog_bg.png'),
          fit: BoxFit.cover,
        ),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          // AI机器人图标区域
          Container(
            height: 180.h,
            child: Stack(
              children: [
                Positioned(
                  left: 20.w,
                  top: 36.h,
                  child: Container(
                    alignment: Alignment.centerLeft,
                    child: Image.asset(
                      'assets/images/ai_review_bot_icon.png',
                      width: 560.w,
                      height: 139.h,
                      alignment: Alignment.centerLeft,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 20.h),

          // 白色背景容器，包含评星区域和AI评阅内容
          Container(
            width: double.infinity,

            padding: EdgeInsets.all(20.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(bottomLeft: Radius.circular(20.r), bottomRight: Radius.circular(20.r)),
            ),
            child: Column(
              children: [
                // 评星区域
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(left: 30.w, top: 39.h, bottom: 39.h, right: 20.w),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Row(
                    children: [
                      Text(
                        '评星',
                        style: TextStyle(
                          fontSize: 26.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(width: 20.w),
                      // 星星评分
                      _buildInteractiveRating(reportDetail),
                    ],
                  ),
                ),

                SizedBox(height: 20.h),

                // AI评阅内容区域
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: _buildReviewCommentsInput(reportDetail),
                ),
              ],
            ),
          ),

          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  // 构建底部按钮
  Widget _buildBottomButton() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _submitReview,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          minimumSize: Size(double.infinity, 88.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.r),
          ),
          elevation: 0,
          padding: EdgeInsets.symmetric(vertical: 12.h),
        ),
        child: Text(
          '提交',
          style: TextStyle(
            fontSize: 30.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}