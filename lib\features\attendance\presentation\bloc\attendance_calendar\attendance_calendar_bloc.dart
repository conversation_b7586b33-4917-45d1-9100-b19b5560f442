/// -----
/// attendance_calendar_bloc.dart
///
/// 签到日历BLoC
/// 管理签到日历页面的状态和业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import '../../../../../core/storage/local_storage.dart';
import '../../../../../core/utils/logger.dart';
import '../../../../internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import '../../../../internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import '../../../domain/usecases/get_sign_in_list_usecase.dart';
import 'attendance_calendar_event.dart';
import 'attendance_calendar_state.dart';

/// 签到日历BLoC
///
/// 管理签到日历页面的状态，包括加载签到列表、切换月份等功能
class AttendanceCalendarBloc extends Bloc<AttendanceCalendarEvent, AttendanceCalendarState> {
  final GetSignInListUseCase _getSignInListUseCase;
  final PlanListGlobalBloc _planListGlobalBloc;
  final LocalStorage _localStorage;
  
  static const String _tag = 'AttendanceCalendarBloc';

  AttendanceCalendarBloc({
    required GetSignInListUseCase getSignInListUseCase,
    required PlanListGlobalBloc planListGlobalBloc,
    required LocalStorage localStorage,
  })  : _getSignInListUseCase = getSignInListUseCase,
        _planListGlobalBloc = planListGlobalBloc,
        _localStorage = localStorage,
        super(const AttendanceCalendarInitialState()) {
    on<LoadAttendanceCalendarEvent>(_onLoadAttendanceCalendar);
    on<RefreshAttendanceCalendarEvent>(_onRefreshAttendanceCalendar);
    on<ChangeMonthEvent>(_onChangeMonth);
  }

  /// 处理加载签到日历事件
  Future<void> _onLoadAttendanceCalendar(
    LoadAttendanceCalendarEvent event,
    Emitter<AttendanceCalendarState> emit,
  ) async {
    Logger.info(_tag, '开始加载签到日历数据');
    
    emit(const AttendanceCalendarLoadingState());

    // 获取实习计划ID
    final planId = await _getPlanId(event.planId);
    if (planId == null) {
      Logger.error(_tag, '无法获取实习计划ID');
      emit(const AttendanceCalendarErrorState(message: '无法获取实习计划信息，请稍后重试'));
      return;
    }

    // 获取查询参数
    final now = DateTime.now();
    final queryYear = event.year ?? now.year;
    final queryMonth = event.month ?? now.month;

    Logger.info(_tag, '查询参数: planId=$planId, year=$queryYear, month=$queryMonth');

    // 调用用例获取签到列表
    final result = await _getSignInListUseCase(GetSignInListParams(
      planId: planId,
      year: queryYear,
      month: queryMonth,
    ));

    result.fold(
      (failure) {
        Logger.error(_tag, '加载签到日历失败: ${failure.message}');
        emit(AttendanceCalendarErrorState(message: failure.message));
      },
      (records) {
        Logger.info(_tag, '成功加载签到日历: ${records.length} 条记录');
        emit(AttendanceCalendarLoadedState(
          records: records,
          year: queryYear,
          month: queryMonth,
          planId: planId,
        ));
      },
    );
  }

  /// 处理刷新签到日历事件
  Future<void> _onRefreshAttendanceCalendar(
    RefreshAttendanceCalendarEvent event,
    Emitter<AttendanceCalendarState> emit,
  ) async {
    Logger.info(_tag, '开始刷新签到日历数据');

    // 保存当前状态中的数据（如果有的话）
    final currentState = state;
    final previousRecords = currentState is AttendanceCalendarLoadedState ? currentState.records : null;
    final previousYear = currentState is AttendanceCalendarLoadedState ? currentState.year : null;
    final previousMonth = currentState is AttendanceCalendarLoadedState ? currentState.month : null;
    final previousPlanId = currentState is AttendanceCalendarLoadedState ? currentState.planId : null;

    // 获取实习计划ID
    final planId = await _getPlanId(event.planId ?? previousPlanId);
    if (planId == null) {
      Logger.error(_tag, '无法获取实习计划ID');
      emit(AttendanceCalendarRefreshErrorState(
        message: '无法获取实习计划信息，请稍后重试',
        previousRecords: previousRecords,
        previousYear: previousYear,
        previousMonth: previousMonth,
        previousPlanId: previousPlanId,
      ));
      return;
    }

    // 获取查询参数
    final now = DateTime.now();
    final queryYear = event.year ?? previousYear ?? now.year;
    final queryMonth = event.month ?? previousMonth ?? now.month;

    Logger.info(_tag, '刷新查询参数: planId=$planId, year=$queryYear, month=$queryMonth');

    // 调用用例获取签到列表
    final result = await _getSignInListUseCase(GetSignInListParams(
      planId: planId,
      year: queryYear,
      month: queryMonth,
    ));

    result.fold(
      (failure) {
        Logger.error(_tag, '刷新签到日历失败: ${failure.message}');
        emit(AttendanceCalendarRefreshErrorState(
          message: failure.message,
          previousRecords: previousRecords,
          previousYear: previousYear,
          previousMonth: previousMonth,
          previousPlanId: previousPlanId,
        ));
      },
      (records) {
        Logger.info(_tag, '成功刷新签到日历: ${records.length} 条记录');
        emit(AttendanceCalendarRefreshSuccessState(
          records: records,
          year: queryYear,
          month: queryMonth,
          planId: planId,
        ));
      },
    );
  }

  /// 处理切换月份事件
  Future<void> _onChangeMonth(
    ChangeMonthEvent event,
    Emitter<AttendanceCalendarState> emit,
  ) async {
    Logger.info(_tag, '切换月份: ${event.year}年${event.month}月');

    // 获取实习计划ID
    final currentState = state;
    final currentPlanId = currentState is AttendanceCalendarLoadedState ? currentState.planId : null;
    final planId = await _getPlanId(event.planId ?? currentPlanId);
    
    if (planId == null) {
      Logger.error(_tag, '无法获取实习计划ID');
      emit(const AttendanceCalendarErrorState(message: '无法获取实习计划信息，请稍后重试'));
      return;
    }

    emit(const AttendanceCalendarLoadingState());

    // 调用用例获取新月份的签到列表
    final result = await _getSignInListUseCase(GetSignInListParams(
      planId: planId,
      year: event.year,
      month: event.month,
    ));

    result.fold(
      (failure) {
        Logger.error(_tag, '切换月份失败: ${failure.message}');
        emit(AttendanceCalendarErrorState(message: failure.message));
      },
      (records) {
        Logger.info(_tag, '成功切换到${event.year}年${event.month}月: ${records.length} 条记录');
        emit(AttendanceCalendarLoadedState(
          records: records,
          year: event.year,
          month: event.month,
          planId: planId,
        ));
      },
    );
  }

  /// 获取实习计划ID
  /// 
  /// 优先级：传入的planId > 全局状态 > 本地存储 > 默认值
  Future<int?> _getPlanId(int? providedPlanId) async {
    // 如果提供了planId，直接使用
    if (providedPlanId != null && providedPlanId > 0) {
      Logger.info(_tag, '使用提供的planId: $providedPlanId');
      return providedPlanId;
    }

    // 从全局状态获取
    final globalState = _planListGlobalBloc.state;
    if (globalState is PlanListGlobalLoadedState && globalState.currentPlanId != null) {
      final planId = int.tryParse(globalState.currentPlanId!);
      if (planId != null && planId > 0) {
        Logger.info(_tag, '从全局状态获取planId: $planId');
        return planId;
      }
    }

    // 从本地存储获取
    final storedPlanId = _localStorage.getString('current_plan_id');
    if (storedPlanId != null && storedPlanId.isNotEmpty) {
      final planId = int.tryParse(storedPlanId);
      if (planId != null && planId > 0) {
        Logger.info(_tag, '从本地存储获取planId: $planId');
        return planId;
      }
    }

    // 使用默认值
    Logger.warning(_tag, '使用默认planId: 8');
    return 8;
  }
}
