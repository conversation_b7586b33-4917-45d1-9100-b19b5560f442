/// -----
/// internship_application_request_model_test.dart
///
/// 实习申请请求模型测试
/// 测试直辖市地址处理逻辑
///
/// <AUTHOR>
/// @date 2025-07-11
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/internship/data/models/internship_application_request_model.dart';

void main() {
  group('InternshipApplicationRequestModel', () {
    group('fromFormData - 直辖市处理测试', () {
      test('企业所在地为北京市时，province和city都应该设置为北京市', () {
        // Arrange
        final companyInfo = {
          'location': '北京市/东城区/王府井',
          'name': '测试企业',
          'creditCode': '*********',
          'address': '测试地址',
          'contactPerson': '张三',
          'contactPhone': '13800138000',
          'email': '<EMAIL>',
          'size': '100-500人',
          'type': '民企',
          'industry': 'IT',
        };
        
        final positionInfo = {
          'location': '广东省/深圳市/南山区',
          'name': '软件工程师',
          'department': '技术部',
          'supervisor': '李四',
          'supervisorPhone': '13900139000',
          'address': '科技园',
          'description': '开发工作',
          'positionCategory': '技术类',
        };
        
        final internshipInfo = {
          'startDate': '2023-01-01',
          'endDate': '2023-06-30',
          'internshipType': '自主联系',
          'professionalMatch': '基本匹配',
          'salary': '3000',
          'accommodationType': '自行解决',
          'accommodationAddress': '测试住宿地址',
          'provideMeals': '否',
          'specialCircumstances': '无',
        };

        // Act
        final result = InternshipApplicationRequestModel.fromFormData(
          planId: 'test-plan-id',
          companyInfo: companyInfo,
          positionInfo: positionInfo,
          internshipInfo: internshipInfo,
        );

        // Assert
        expect(result.province, '北京市');
        expect(result.city, '北京市');
      });

      test('岗位所在省市为上海市时，jobProvince和jobCity都应该设置为上海市', () {
        // Arrange
        final companyInfo = {
          'location': '广东省/深圳市/南山区',
          'name': '测试企业',
          'creditCode': '*********',
          'address': '测试地址',
          'contactPerson': '张三',
          'contactPhone': '13800138000',
          'email': '<EMAIL>',
          'size': '100-500人',
          'type': '民企',
          'industry': 'IT',
        };
        
        final positionInfo = {
          'location': '上海市/黄浦区/外滩',
          'name': '软件工程师',
          'department': '技术部',
          'supervisor': '李四',
          'supervisorPhone': '13900139000',
          'address': '外滩金融中心',
          'description': '开发工作',
          'positionCategory': '技术类',
        };
        
        final internshipInfo = {
          'startDate': '2023-01-01',
          'endDate': '2023-06-30',
          'internshipType': '自主联系',
          'professionalMatch': '基本匹配',
          'salary': '3000',
          'accommodationType': '自行解决',
          'accommodationAddress': '测试住宿地址',
          'provideMeals': '否',
          'specialCircumstances': '无',
        };

        // Act
        final result = InternshipApplicationRequestModel.fromFormData(
          planId: 'test-plan-id',
          companyInfo: companyInfo,
          positionInfo: positionInfo,
          internshipInfo: internshipInfo,
        );

        // Assert
        expect(result.jobProvince, '上海市');
        expect(result.jobCity, '上海市');
      });

      test('测试所有四个直辖市的处理', () {
        final municipalities = ['北京市', '天津市', '重庆市', '上海市'];
        
        for (final municipality in municipalities) {
          // Arrange
          final companyInfo = {
            'location': '$municipality/测试区/测试街道',
            'name': '测试企业',
            'creditCode': '*********',
            'address': '测试地址',
            'contactPerson': '张三',
            'contactPhone': '13800138000',
            'email': '<EMAIL>',
            'size': '100-500人',
            'type': '民企',
            'industry': 'IT',
          };
          
          final positionInfo = {
            'location': '$municipality/测试区/测试街道',
            'name': '软件工程师',
            'department': '技术部',
            'supervisor': '李四',
            'supervisorPhone': '13900139000',
            'address': '测试地址',
            'description': '开发工作',
            'positionCategory': '技术类',
          };
          
          final internshipInfo = {
            'startDate': '2023-01-01',
            'endDate': '2023-06-30',
            'internshipType': '自主联系',
            'professionalMatch': '基本匹配',
            'salary': '3000',
            'accommodationType': '自行解决',
            'accommodationAddress': '测试住宿地址',
            'provideMeals': '否',
            'specialCircumstances': '无',
          };

          // Act
          final result = InternshipApplicationRequestModel.fromFormData(
            planId: 'test-plan-id',
            companyInfo: companyInfo,
            positionInfo: positionInfo,
            internshipInfo: internshipInfo,
          );

          // Assert
          expect(result.province, municipality, reason: '企业所在地 $municipality 的province字段应该设置为 $municipality');
          expect(result.city, municipality, reason: '企业所在地 $municipality 的city字段应该设置为 $municipality');
          expect(result.jobProvince, municipality, reason: '岗位所在省市 $municipality 的jobProvince字段应该设置为 $municipality');
          expect(result.jobCity, municipality, reason: '岗位所在省市 $municipality 的jobCity字段应该设置为 $municipality');
        }
      });

      test('非直辖市地址应该正常处理', () {
        // Arrange
        final companyInfo = {
          'location': '广东省/深圳市/南山区',
          'name': '测试企业',
          'creditCode': '*********',
          'address': '测试地址',
          'contactPerson': '张三',
          'contactPhone': '13800138000',
          'email': '<EMAIL>',
          'size': '100-500人',
          'type': '民企',
          'industry': 'IT',
        };
        
        final positionInfo = {
          'location': '江苏省/南京市/鼓楼区',
          'name': '软件工程师',
          'department': '技术部',
          'supervisor': '李四',
          'supervisorPhone': '13900139000',
          'address': '测试地址',
          'description': '开发工作',
          'positionCategory': '技术类',
        };
        
        final internshipInfo = {
          'startDate': '2023-01-01',
          'endDate': '2023-06-30',
          'internshipType': '自主联系',
          'professionalMatch': '基本匹配',
          'salary': '3000',
          'accommodationType': '自行解决',
          'accommodationAddress': '测试住宿地址',
          'provideMeals': '否',
          'specialCircumstances': '无',
        };

        // Act
        final result = InternshipApplicationRequestModel.fromFormData(
          planId: 'test-plan-id',
          companyInfo: companyInfo,
          positionInfo: positionInfo,
          internshipInfo: internshipInfo,
        );

        // Assert
        expect(result.province, '广东省');
        expect(result.city, '深圳市');
        expect(result.jobProvince, '江苏省');
        expect(result.jobCity, '南京市');
      });

      test('测试city是直辖市但province不是的情况', () {
        // Arrange
        final companyInfo = {
          'location': '错误省份/北京市/朝阳区',
          'name': '测试企业',
          'creditCode': '*********',
          'address': '测试地址',
          'contactPerson': '张三',
          'contactPhone': '13800138000',
          'email': '<EMAIL>',
          'size': '100-500人',
          'type': '民企',
          'industry': 'IT',
        };

        final positionInfo = {
          'location': '错误省份/上海市/浦东新区',
          'name': '软件工程师',
          'department': '技术部',
          'supervisor': '李四',
          'supervisorPhone': '13900139000',
          'address': '测试地址',
          'description': '开发工作',
          'positionCategory': '技术类',
        };

        final internshipInfo = {
          'startDate': '2023-01-01',
          'endDate': '2023-06-30',
          'internshipType': '自主联系',
          'professionalMatch': '基本匹配',
          'salary': '3000',
          'accommodationType': '自行解决',
          'accommodationAddress': '测试住宿地址',
          'provideMeals': '否',
          'specialCircumstances': '无',
        };

        // Act
        final result = InternshipApplicationRequestModel.fromFormData(
          planId: 'test-plan-id',
          companyInfo: companyInfo,
          positionInfo: positionInfo,
          internshipInfo: internshipInfo,
        );

        // Assert - 企业地址应该被修正为直辖市
        expect(result.province, '北京市');
        expect(result.city, '北京市');

        // Assert - 岗位地址应该被修正为直辖市
        expect(result.jobProvince, '上海市');
        expect(result.jobCity, '上海市');
      });
    });
  });
}
