/// -----
/// internship_score_repository_impl.dart
/// 
/// 实习成绩仓库实现类
///
/// <AUTHOR>
/// @date 2025-07-16
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/features/internship/data/datasources/remote/internship_score_remote_data_source.dart';
import 'package:flutter_demo/features/internship/data/models/score_submit_request_model.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_score.dart';
import 'package:flutter_demo/features/internship/domain/entities/score_item.dart';
import 'package:flutter_demo/features/internship/domain/repositories/internship_score_repository.dart';

/// 实习成绩仓库实现类
///
/// 实现实习成绩仓库接口，处理数据获取和错误处理
class InternshipScoreRepositoryImpl implements InternshipScoreRepository {
  final InternshipScoreRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  const InternshipScoreRepositoryImpl({
    required InternshipScoreRemoteDataSource remoteDataSource,
    required NetworkInfo networkInfo,
  })  : _remoteDataSource = remoteDataSource,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, List<InternshipScore>>> getStudentScoreList({
    required String planId,
    required int type,
  }) async {
    // 检查网络连接
    if (await _networkInfo.isConnected) {
      try {
        // 调用远程数据源
        final scoreModels = await _remoteDataSource.getStudentScoreList(
          planId: planId,
          type: type,
        );
        
        // 转换为实体类列表
        final scores = scoreModels.map((model) => model.toEntity()).toList();
        
        return Right(scores);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取学生评分列表失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }

  @override
  Future<Either<Failure, List<ScoreItem>>> getScoreItems({
    required String planId,
  }) async {
    // 检查网络连接
    if (await _networkInfo.isConnected) {
      try {
        // 调用远程数据源
        final scoreItemModels = await _remoteDataSource.getScoreItems(
          planId: planId,
        );

        // 转换为实体类列表
        final scoreItems = scoreItemModels.map((model) => model.toEntity()).toList();

        return Right(scoreItems);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取评分项列表失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }

  @override
  Future<Either<Failure, List<ScoreItem>>> getScoreDetail({
    required String recordId,
  }) async {
    // 检查网络连接
    if (await _networkInfo.isConnected) {
      try {
        // 调用远程数据源
        final scoreItemModels = await _remoteDataSource.getScoreDetail(
          recordId: recordId,
        );

        // 转换为实体类列表
        final scoreItems = scoreItemModels.map((model) => model.toEntity()).toList();

        return Right(scoreItems);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取评分详情失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }

  @override
  Future<Either<Failure, void>> submitScore({
    required String planId,
    required String studentId,
    required String studentName,
    required List<ScoreItem> scoreItems,
  }) async {
    // 检查网络连接
    if (await _networkInfo.isConnected) {
      try {
        // 构造请求数据
        final requestItems = scoreItems.map((item) => ScoreSubmitItemModel(
          content: item.content,
          score: item.score ?? 0, // 如果分数为null，使用0作为默认值
          title: item.title,
        )).toList();

        final request = ScoreSubmitRequestModel(
          item: requestItems,
          planId: planId,
          studentId: studentId,
          studentName: studentName,
        );

        // 调用远程数据源
        await _remoteDataSource.submitScore(request: request);

        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('提交评分失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }
}
