/// -----
/// get_student_grade_list_usecase.dart
///
/// 获取学生成绩列表用例，封装获取学生成绩数据的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/features/grade/domain/entities/student_grade.dart';
import 'package:flutter_demo/features/grade/domain/repositories/student_grade_repository.dart';

/// 获取学生成绩列表用例
///
/// 封装获取学生成绩数据的业务逻辑
class GetStudentGradeListUseCase implements UseCase<StudentGrade, GetStudentGradeListParams> {
  final StudentGradeRepository _repository;

  GetStudentGradeListUseCase(this._repository);

  @override
  Future<Either<Failure, StudentGrade>> call(GetStudentGradeListParams params) {
    return _repository.getStudentGradeList(params.planId);
  }
}

/// 获取学生成绩列表参数
///
/// 包含获取学生成绩列表所需的参数
class GetStudentGradeListParams extends Equatable {
  /// 实习计划ID
  final String planId;

  const GetStudentGradeListParams({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}
