---
type: "agent_requested"
description: "api rules"
---
# API 规则

## 亿硕教育 API 规范
- 基础 URL: `http://**************:8089/userservice/userservice/`
- 响应格式: `{data, resultCode, resultMsg}`

## 请求格式规则
所有请求应使用 POST 方法，并包含以下头信息:
```json
{
  "Content-type": "application/json",
  "Accept": "application/json",
  "token": "Bearer {token}"
}
```

## 网络客户端配置规则
- 使用 `Dio` 作为HTTP客户端
- 在 `lib/core/network/api/dio_client.dart` 中实现 `DioClient` 类
- 配置基础URL、超时设置、默认头信息等
- 注册所有拦截器

### 必须注册的拦截器
1. **日志拦截器**: 记录请求和响应的详细信息
   - 在开发环境中启用，生产环境禁用
   
2. **认证拦截器**: 处理token的添加、刷新和过期逻辑
   - 在请求头中添加token
   - 处理token过期的情况，自动刷新token并重试请求

3. **错误处理拦截器**: 统一处理网络错误和服务器错误
   - 转换错误为应用内的异常类型

4. **缓存拦截器**: 实现请求缓存策略
   - 在无网络或指定情况下使用缓存数据

## 网络状态监控规则
- 实现 `NetworkInfo` 接口来检查网络连接状态
- 在请求前检查网络状态，避免无网络时发送请求

## 错误处理规则

### 异常类型
- `ServerException`: 服务器错误
- `CacheException`: 缓存错误
- `NetworkException`: 网络错误
- `UnauthorizedException`: 未授权错误
- `ForbiddenException`: 禁止访问错误
- `NotFoundException`: 资源不存在错误
- `RequestCancelledException`: 请求取消错误
- `UnknownException`: 未知错误

### 失败类型
- `ServerFailure`: 服务器失败
- `CacheFailure`: 缓存失败
- `NetworkFailure`: 网络失败

### 错误处理流程
1. 数据源层抛出异常
2. 仓库层捕获异常并转换为失败
3. 用例层返回 Either<Failure, T> 类型
4. BLoC 层处理失败并更新状态

## API 服务实现规则
- API 服务应实现在数据源层
- 使用 `DioClient` 发送网络请求
- 处理响应格式和错误情况
- 将响应数据转换为模型对象
---
type: "agent_requested"
description: "Example description"
---
# 架构规则

## 整体架构规则

### 必须使用 Clean Architecture 架构
- **表现层(Presentation)**: UI 组件和页面、BLoC 状态管理、路由管理
- **领域层(Domain)**: 业务实体、用例、仓库接口
- **数据层(Data)**: 仓库实现、数据源、数据模型

### 架构原则
1. **依赖规则**: 外层依赖内层，内层不依赖外层
   - 表现层依赖领域层和数据层
   - 领域层不依赖表现层和数据层
   - 数据层依赖领域层，不依赖表现层

2. **依赖注入**: 使用 GetIt 进行依赖注入，解耦组件
   - 在应用启动时注册所有依赖
   - 按模块组织依赖注册
   - 遵循依赖倒置原则

3. **状态管理**: 使用 BLoC 模式管理状态
   - 将 UI 与业务逻辑分离
   - 通过事件驱动状态变化
   - 保持状态不可变

4. **路由管理**: 使用 go_router 进行声明式路由
   - 集中管理所有路由定义
   - 统一页面转场动画
   - 使用路由抽象层简化导航

5. **DRY原则**: 符合DRY原则（Don't Repeat Yourself）
   - 避免重复代码，提取公共逻辑到工具类或基类
   - 相似功能应封装为可复用的组件或方法
   - 重复的业务逻辑应抽象为用例或服务类
   - 重复的UI组件应提取为可复用的Widget
   - 重复的网络请求逻辑应封装为统一的API客户端

## 文件结构规则

### 必须遵循的目录结构
```
lib/
  ├── core/                # 核心工具和通用组件
  │   ├── common/          # 通用组件
  │   ├── config/          # 配置文件
  │   │   ├── env/         # 环境配置
  │   │   └── injection/   # 依赖注入配置
  │   ├── constants/       # 常量定义
  │   ├── error/           # 错误处理
  │   │   ├── exceptions/  # 异常类
  │   │   └── failures/    # 失败类
  │   ├── network/         # 网络相关
  │   │   ├── api/         # API客户端
  │   │   └── interceptors/# 拦截器
  │   ├── router/          # 路由配置
  │   ├── storage/         # 本地存储
  │   ├── theme/           # 主题配置
  │   ├── utils/           # 工具类
  │   └── widgets/         # 共享UI组件
  │       ├── buttons/     # 按钮组件
  │       ├── dialogs/     # 对话框组件
  │       ├── forms/       # 表单组件
  │       ├── indicators/  # 指示器组件
  │       └── states/      # 状态组件（加载、空数据等）
  └── features/            # 功能模块
      ├── [feature_name]/  # 功能模块名
      │   ├── data/        # 数据层
      │   │   ├── datasources/  # 数据源
      │   │   │   ├── local/    # 本地数据源
      │   │   │   └── remote/   # 远程数据源
      │   │   ├── models/       # 数据模型
      │   │   └── repositories/ # 仓库实现
      │   ├── domain/      # 领域层
      │   │   ├── entities/     # 业务实体
      │   │   ├── repositories/ # 仓库接口
      │   │   └── usecases/     # 用例
      │   ├── presentation/ # 表现层
      │   │   ├── bloc/         # BLoC
      │   │   ├── pages/        # 页面
      │   │   └── widgets/      # UI组件
      │   └── di/          # 依赖注入
```

## 依赖注入规则

### 依赖注册方式
- **单例注册**: 使用 `registerLazySingleton` 注册长期存在的服务（如仓库、数据源）
- **工厂注册**: 使用 `registerFactory` 注册短期存在的对象（如 BLoC、用例）
- **作用域注册**: 使用 `registerScopedSingleton` 注册有作用域的单例（如用户会话）
- **异步初始化**: 使用 `registerSingletonAsync` 注册需要异步初始化的依赖
- **依赖获取**: 使用 `getIt<T>()` 获取依赖，避免直接创建实例

### 依赖注入最佳实践
- 在应用启动时初始化所有依赖
- 按功能模块组织依赖注册
- 遵循依赖倒置原则，依赖抽象而非具体实现
- 使用工厂方法创建复杂对象
- 在测试中使用 `registerMock` 替换真实实现
---
type: "agent_requested"
description: "Example description"
---
# 编码规则

## 命名约定
- **类名**: 使用 PascalCase (例如: `ContentRepository`)
- **变量和方法**: 使用 camelCase (例如: `getVideoList()`)
- **常量**: 使用 UPPER_SNAKE_CASE (例如: `MAX_RETRY_COUNT`)
- **私有成员**: 使用下划线前缀 (例如: `_apiClient`)
- **文件名**: 使用 snake_case (例如: `content_repository.dart`)

## 分层命名规范
- **实体**: 使用领域名称 (例如: `ContentItem`)
- **模型**: 使用领域名称加 Model 后缀 (例如: `ContentItemModel`)
- **仓库接口**: 使用领域名称加 Repository 后缀 (例如: `ContentRepository`)
- **仓库实现**: 使用领域名称加 RepositoryImpl 后缀 (例如: `ContentRepositoryImpl`)
- **数据源**: 使用领域名称加 DataSource 后缀 (例如: `ContentRemoteDataSource`)
- **用例**: 使用动词加领域名称 (例如: `GetContentDetail`)

## BLoC 模式规范
- 每个功能模块应有自己的 BLoC
- BLoC 应分为三个文件: `xxx_bloc.dart`, `xxx_event.dart`, `xxx_state.dart`
- 状态应该是不可变的(immutable)，继承自 Equatable
- 事件应该是明确的动作，继承自 Equatable
- BLoC 应该在 DI 容器中注册为工厂

## 导航规范
- 使用 go_router 进行导航
- 路由常量定义在 `lib/core/router/route_constants.dart` 中
- 路由配置定义在 `lib/core/router/app_router.dart` 中
- 使用 context.push() 或 context.go() 进行导航
- 使用路由抽象层简化导航操作

### 路由结构规范
- 路由常量使用 AppRoutes 类管理
- 路由参数键名使用 RouteParams 类管理
- 路由配置使用 AppRouter 类管理
- 路由导航使用 AppNavigator 抽象层

### 路由动画最佳实践
1. **区分不同类型的路由动画**:
   - 底部导航栏切换：使用 `NoTransitionPage` 避免动画
   - 普通页面跳转：使用标准的滑动或淡入淡出动画
   - 模态页面：使用从底部滑入的动画

2. **统一动画效果**:
   - 为所有页面跳转定义统一的动画效果
   - 避免不同页面间的动画不一致

3. **避免嵌套路由动画问题**:
   - 合理使用 ShellRoute 和 GoRoute
   - 避免过深的嵌套路由
---
type: "always_apply"
---

# 注释规则

## 文件注释规则
- 每个新创建的文件需要添加文件注释，说明该文件的主要功能和用途
- 文件注释应放在文件的最开头，使用相应语言的注释标记
- 所有文件必须包含作者信息：Mr.Wang
- 所有文件必须包含版权声明：Copyright © 2025
- 文件头注释模板应参考以下格式（根据不同语言的注释格式进行调整）：

```dart
/// -----
/// [文件名]
/// 
/// [文件功能描述]
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----
```

## 类注释规则
- 每个类都应该有清晰的注释说明其用途和职责
- 类注释应包含类的主要功能、使用场景和重要说明
- 对于复杂的类，应该说明其设计模式和架构考虑

## 方法注释规则
- 公共方法必须添加注释，说明方法的功能、参数和返回值
- 复杂的私有方法也应该添加注释
- 方法注释应包含：
  - 方法功能描述
  - 参数说明（如果有）
  - 返回值说明（如果有）
  - 异常说明（如果会抛出异常）
  - 使用示例（对于复杂方法）

## 代码内注释规则
- 复杂的业务逻辑应该添加行内注释
- 重要的算法步骤应该添加说明
- 临时解决方案或待优化的代码应该添加 TODO 注释
- 已知问题或限制应该添加 FIXME 注释

## 注释质量要求
- 注释应该简洁明了，避免冗余
- 注释应该与代码保持同步，及时更新
- 避免无意义的注释，如 `// 设置变量 x = 1`
- 使用中文注释，确保团队成员都能理解

## 文档注释规则
- 对于对外提供的API，应该使用文档注释格式
- 文档注释应该包含完整的使用说明和示例
- 重要的配置类和工具类应该有详细的文档注释
---
type: "always_apply"
---

# 导包规则

## 导包顺序规则
导入包时应按照以下顺序排列：
1. **Dart 标准库**: 如 `dart:core`, `dart:async`, `dart:io` 等
2. **Flutter 框架库**: 如 `package:flutter/material.dart`, `package:flutter/widgets.dart` 等
3. **第三方库**: 按字母顺序排列，如 `package:dio/dio.dart`, `package:get_it/get_it.dart` 等
4. **项目内部库**: 按字母顺序排列，使用绝对路径

## 导包路径规范
- **强制使用绝对路径**: 所有导入都必须使用 `package:flutter_demo/` 开头的绝对路径
- **禁止使用相对路径**: 不允许使用 `../`, `./` 等相对路径导入
- **具体导入**: 避免使用 `*` 导入，应指定具体的类或函数

## 导包分组规则
在导包时，应使用空行将不同类型的导入分组：

```dart
import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/auth/domain/entities/user.dart';
```

## 导包别名规则
当存在命名冲突时，应使用 `as` 关键字为导入的库设置别名：

```dart
import 'package:flutter_demo/features/auth/domain/entities/user.dart' as auth;
import 'package:flutter_demo/features/profile/domain/entities/user.dart' as profile;
```

## 条件导包规则
对于平台特定的导入，应使用条件导入：

```dart
import 'package:flutter_demo/core/utils/platform_utils_stub.dart'
    if (dart.library.io) 'package:flutter_demo/core/utils/platform_utils_mobile.dart'
    if (dart.library.html) 'package:flutter_demo/core/utils/platform_utils_web.dart';
```

## 导包最佳实践
- 定期清理未使用的导入
- 使用IDE的自动导入功能，但要检查导入路径是否正确
- 避免循环依赖，如果出现循环依赖，需要重新设计架构
- 对于大型项目，考虑使用 barrel exports 来简化导入

## 禁止的导包方式
❌ 错误示例：
```dart
// 错误：使用相对路径
import '../../../core/constants/constants.dart';
import '../../domain/entities/user.dart';
import './auth_bloc.dart';

// 错误：顺序混乱
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'dart:async';

// 错误：使用通配符导入
import 'package:flutter_demo/core/constants/*';
```

✅ 正确示例：
```dart
// Dart 标准库
import 'dart:async';
import 'dart:convert';

// Flutter 框架库
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 第三方库（按字母顺序）
import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

// 项目内部库（按字母顺序，使用绝对路径）
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/auth/domain/entities/user.dart';
import 'package:flutter_demo/features/auth/presentation/bloc/auth_bloc.dart';
```
---
type: "agent_requested"
description: "Example description"
---
# 列表页面规则

## 推荐第三方库
- **下拉刷新和上拉加载**: `pull_to_refresh` 或 `flutter_easyrefresh`
- **滚动检测**: `visibility_detector` 或 `flutter_visibility_detector`
- **滚动控制**: `scrollable_positioned_list` 用于滚动到特定位置

## BLoC 状态管理集成规则

### 事件定义规则
必须包含以下事件：
- `LoadXxxListEvent`: 初始加载列表
- `RefreshXxxListEvent`: 下拉刷新列表
- `LoadMoreXxxListEvent`: 上拉加载更多

### 状态定义规则
必须包含以下状态：
- `XxxListInitial`: 初始状态
- `XxxListLoading`: 加载状态（区分首次加载和加载更多）
- `XxxListSuccess`: 成功状态（包含数据和是否到达末页标识）
- `XxxListFailure`: 失败状态（包含错误信息和已有数据）
- `XxxListRefreshSuccess`: 刷新成功状态
- `XxxListRefreshFailure`: 刷新失败状态
- `XxxListLoadMoreSuccess`: 加载更多成功状态
- `XxxListLoadMoreFailure`: 加载更多失败状态

### BLoC 实现规则
- 在 BLoC 中维护当前页码和页面大小
- 使用 `_hasReachedMax` 标识是否到达末页
- 处理初始加载、下拉刷新、上拉加载更多三种场景

## 加载状态的UI展示规则
1. **初始加载**：显示居中的加载指示器
2. **下拉刷新**：显示下拉刷新指示器（如水滴效果）
3. **上拉加载更多**：显示底部加载指示器
4. **空数据状态**：显示空数据缺省页
5. **错误状态**：显示错误缺省页，并提供重试按钮

## 分页逻辑处理规则
1. **页码管理**：在 BLoC 中维护当前页码和页面大小
2. **初始加载**：加载第一页数据
3. **下拉刷新**：重置页码为1，重新加载第一页数据
4. **上拉加载更多**：页码加1，加载下一页数据
5. **判断是否到达末页**：根据返回数据量是否小于页面大小来判断

## 错误处理机制规则
1. **网络错误**：显示网络错误缺省页，提供重试按钮
2. **服务器错误**：显示服务器错误缺省页，提供重试按钮
3. **刷新失败**：显示刷新失败提示，保留原有数据
4. **加载更多失败**：显示加载更多失败提示，保留已加载数据，提供重试选项

## 性能优化规则
1. **列表项缓存**：使用 `ListView.builder` 而非 `ListView`
2. **懒加载图片**：使用 `CachedNetworkImage` 加载网络图片
3. **防抖动处理**：避免短时间内多次触发加载更多
4. **滚动优化**：使用 `const` 构造函数和 `RepaintBoundary` 减少重绘

## 用例实现规则
- 创建专门的用例类处理列表数据获取
- 用例参数应包含页码和页面大小
- 返回 Either<Failure, List<T>> 类型

## 仓库实现规则
- 在仓库中检查网络状态
- 处理网络异常和服务器异常
- 将异常转换为相应的失败类型

## 数据源实现规则
- 使用 `DioClient` 发送网络请求
- 处理分页参数
- 将响应数据转换为模型列表
---
type: "agent_requested"
description: "Example description"
---
# 日志记录规则

## 日志级别定义

应用中的日志分为以下几个级别，按严重程度从低到高排序：

1. **VERBOSE**: 最详细的日志信息，通常只在开发阶段使用
   - 用于跟踪代码执行流程
   - 记录临时调试信息
   - 仅在开发环境启用

2. **DEBUG**: 调试信息，帮助开发人员理解应用行为
   - 记录函数调用参数和返回值
   - 记录状态变化
   - 记录条件分支选择
   - 在开发和测试环境启用

3. **INFO**: 一般信息，记录应用正常运行过程中的重要事件
   - 记录用户操作（如登录、注销）
   - 记录页面导航
   - 记录重要业务流程的开始和完成
   - 在所有环境中启用

4. **WARNING**: 警告信息，表示可能的问题，但不影响主要功能
   - 记录非关键异常
   - 记录性能问题
   - 记录即将废弃的API使用
   - 在所有环境中启用

5. **ERROR**: 错误信息，表示应用遇到了严重问题
   - 记录影响功能的异常
   - 记录网络请求失败
   - 记录数据库操作失败
   - 在所有环境中启用

6. **FATAL**: 致命错误，表示应用无法继续运行
   - 记录导致应用崩溃的异常
   - 记录严重的资源不足问题
   - 在所有环境中启用

## 日志格式规范

所有日志应遵循以下格式：

```
[级别] [时间] [标签] [消息] [可选:异常信息] [可选:堆栈跟踪]
```

其中：
- **级别**: 日志级别（VERBOSE, DEBUG, INFO, WARNING, ERROR, FATAL）
- **时间**: 格式为 `yyyy-MM-dd HH:mm:ss.SSS`
- **标签**: 用于标识日志来源，通常是类名
- **消息**: 具体的日志内容
- **异常信息**: 如果有异常，记录异常类型和消息
- **堆栈跟踪**: 对于ERROR和FATAL级别，应包含完整的堆栈跟踪

## 日志使用场景指南

### 网络请求日志
- **请求前**: 记录请求URL、方法、头信息和参数（INFO级别）
- **请求成功**: 记录响应状态码和响应数据（DEBUG级别）
- **请求失败**: 记录错误详情和堆栈跟踪（ERROR级别）

### 状态管理日志
- **状态变化**: 记录状态转换（DEBUG级别）
- **事件处理**: 记录收到的事件和处理结果（DEBUG级别）
- **错误状态**: 记录导致错误状态的原因（ERROR级别）

### 生命周期日志
- **页面创建/销毁**: 记录页面生命周期（DEBUG级别）
- **初始化/释放资源**: 记录资源管理（INFO级别）

### 用户操作日志
- **用户交互**: 记录重要的用户操作（INFO级别）
- **操作结果**: 记录操作的结果（INFO/ERROR级别）

## 日志配置规则

应用应支持根据环境配置日志行为：

1. **开发环境**:
   - 启用所有级别的日志
   - 日志输出到控制台
   - 包含详细的堆栈跟踪

2. **测试环境**:
   - 启用INFO级别及以上的日志
   - 日志输出到控制台和文件
   - 包含简化的堆栈跟踪

3. **生产环境**:
   - 启用WARNING级别及以上的日志
   - 日志输出到文件和远程服务器
   - 包含完整的堆栈跟踪
   - 实现日志文件轮转，避免占用过多存储空间

## 敏感信息处理规则

日志中不应包含以下敏感信息：

1. **用户凭证**: 密码、令牌、密钥等
2. **个人身份信息**: 身份证号、银行卡号等
3. **位置信息**: 精确的地理位置信息

对于需要记录的敏感信息，应进行脱敏处理。

## 日志工具实现规则

应用应使用统一的日志工具类，提供以下功能：

1. **不同级别的日志方法**: verbose(), debug(), info(), warning(), error(), fatal()
2. **支持标签**: 每个日志条目都应有标签标识来源
3. **支持异常和堆栈跟踪**: 错误日志应包含异常信息和堆栈跟踪
4. **日志过滤**: 根据级别和标签过滤日志
5. **日志存储**: 支持将日志保存到文件或发送到远程服务器
6. **日志格式化**: 按照规定格式输出日志
---
type: "agent_requested"
description: "Example description"
---
# UI 规则

## 应用栏规则
- **统一使用自定义AppBar**: 需要的页面使用 `CustomAppBar` 组件，而不是标准的 `AppBar`
- **实现位置**: 该组件位于 `lib/core/widgets/custom_app_bar.dart`
- **一致性**: 确保整个应用的导航栏样式一致
- **自定义选项**: 支持标题、返回按钮、操作按钮、背景颜色等自定义选项

## Loading 页面规则
- **统一样式**: 所有 Loading 页面应使用统一的样式和动画
- **实现方式**: 使用 `LoadingWidget` 组件，该组件位于 `lib/core/widgets/loading_widget.dart`
- **状态管理**: 在 BLoC 中使用 `LoadingState` 状态来控制 Loading 的显示和隐藏
- **超时处理**: Loading 状态应设置超时处理，默认 30 秒后转为错误状态
- **取消操作**: 提供取消加载的选项，特别是在耗时操作中

## 缺省页规则
- **类型划分**: 缺省页应根据不同情况划分为以下几种类型:
  - **空数据缺省页**: 当没有数据时显示
  - **网络错误缺省页**: 当网络连接失败时显示
  - **服务器错误缺省页**: 当服务器返回错误时显示
  - **权限错误缺省页**: 当用户没有权限访问内容时显示
  - **搜索无结果缺省页**: 当搜索没有结果时显示
- **实现方式**: 使用 `EmptyStateWidget` 组件，该组件位于 `lib/core/widgets/empty_state_widget.dart`
- **交互设计**: 缺省页应提供明确的用户操作指引，如"重试"、"返回"等按钮
- **状态管理**: 在 BLoC 中使用对应的状态（如 `EmptyState`、`ErrorState` 等）来控制缺省页的显示

## 提示（Snackbar）规则
- **统一使用AppSnackBar**: 应用中所有提示信息应使用 `AppSnackBar` 工具类，而不是直接使用 `ScaffoldMessenger.showSnackBar`
- **实现位置**: 该工具类位于 `lib/core/widgets/app_snack_bar.dart`
- **提示类型**: 根据不同场景使用不同类型的提示:
  - **普通提示**: 使用 `AppSnackBar.show()` 方法，默认样式
  - **成功提示**: 使用 `AppSnackBar.showSuccess()` 方法，绿色背景
  - **错误提示**: 使用 `AppSnackBar.showError()` 方法，红色背景
  - **警告提示**: 使用 `AppSnackBar.showWarning()` 方法，橙色背景
  - **带操作按钮的提示**: 使用 `AppSnackBar.showWithAction()` 方法，包含可点击的操作按钮
- **显示时长**: 根据提示类型和内容长度设置合适的显示时长
  - 普通提示: 默认2秒
  - 成功提示: 默认2秒
  - 错误提示: 默认3秒
  - 警告提示: 默认3秒
  - 带操作按钮的提示: 默认4秒
- **样式规范**:
  - 使用圆角边框（8.r）
  - 使用浮动行为（SnackBarBehavior.floating）
  - 四周边距为16.w
  - 文字大小为28.sp
  - 文字颜色为白色

## 使用场景指南
- **表单提交成功**: 使用成功提示，简短描述操作结果
- **操作错误**: 使用错误提示，说明错误原因和可能的解决方法
- **需要用户确认的操作**: 使用带操作按钮的提示，提供撤销或确认选项
- **网络状态变化**: 使用警告提示，告知用户网络状态
- **BLoC状态监听**: 在BLoC的listener中根据状态显示相应提示
