/// -----
/// profile_injection_container.dart
/// 
/// Profile模块依赖注入容器，注册所有相关依赖
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/profile/data/datasources/remote/user_info_remote_data_source.dart';
import 'package:flutter_demo/features/profile/data/datasources/remote/user_info_remote_data_source_impl.dart';
import 'package:flutter_demo/features/profile/data/repositories/user_info_repository_impl.dart';
import 'package:flutter_demo/features/profile/domain/repositories/user_info_repository.dart';
import 'package:flutter_demo/features/profile/domain/usecases/change_password.dart';
import 'package:flutter_demo/features/profile/domain/usecases/get_user_info.dart';
import 'package:flutter_demo/features/profile/domain/usecases/save_user_avatar.dart';
import 'package:flutter_demo/features/profile/domain/usecases/update_user_gender.dart';
import 'package:flutter_demo/features/profile/presentation/bloc/user_info_bloc.dart';

/// Profile模块依赖注入容器
///
/// 负责注册Profile模块的所有依赖，包括：
/// - 数据源（远程数据源）
/// - 仓库（仓库接口和实现）
/// - 用例（业务逻辑）
/// - BLoC（状态管理）
class ProfileInjectionContainer {
  static final GetIt _getIt = GetIt.instance;

  /// 初始化Profile模块的依赖注入
  ///
  /// 按照Clean Architecture的依赖关系注册所有组件
  /// 注册顺序：数据源 -> 仓库 -> 用例 -> BLoC
  static Future<void> init() async {
    // ===== 数据源 =====
    
    // 注册用户信息远程数据源
    _getIt.registerLazySingleton<UserInfoRemoteDataSource>(
      () => UserInfoRemoteDataSourceImpl(_getIt<DioClient>()),
    );

    // ===== 仓库 =====
    
    // 注册用户信息仓库
    _getIt.registerLazySingleton<UserInfoRepository>(
      () => UserInfoRepositoryImpl(_getIt<UserInfoRemoteDataSource>()),
    );

    // ===== 用例 =====

    // 注册获取用户信息用例
    _getIt.registerLazySingleton<GetUserInfo>(
      () => GetUserInfo(_getIt<UserInfoRepository>()),
    );

    // 注册更新用户性别用例
    _getIt.registerLazySingleton<UpdateUserGender>(
      () => UpdateUserGender(_getIt<UserInfoRepository>()),
    );

    // 注册保存用户头像用例
    _getIt.registerLazySingleton<SaveUserAvatar>(
      () => SaveUserAvatar(_getIt<UserInfoRepository>()),
    );

    // 注册修改用户密码用例
    _getIt.registerLazySingleton<ChangePassword>(
      () => ChangePassword(_getIt<UserInfoRepository>()),
    );

    // ===== BLoC =====

    // 注册用户信息BLoC
    // 注意：BLoC使用单例注册，确保全局状态一致性
    _getIt.registerLazySingleton<UserInfoBloc>(
      () => UserInfoBloc(
        getUserInfo: _getIt<GetUserInfo>(),
        updateUserGender: _getIt<UpdateUserGender>(),
        saveUserAvatar: _getIt<SaveUserAvatar>(),
        changePassword: _getIt<ChangePassword>(),
      ),
    );
  }

  /// 清理Profile模块的依赖注入
  ///
  /// 用于测试或重新初始化时清理已注册的依赖
  static Future<void> reset() async {
    // 清理BLoC（单例）
    if (_getIt.isRegistered<UserInfoBloc>()) {
      _getIt.unregister<UserInfoBloc>();
    }

    // 清理用例
    if (_getIt.isRegistered<GetUserInfo>()) {
      _getIt.unregister<GetUserInfo>();
    }
    if (_getIt.isRegistered<UpdateUserGender>()) {
      _getIt.unregister<UpdateUserGender>();
    }
    if (_getIt.isRegistered<SaveUserAvatar>()) {
      _getIt.unregister<SaveUserAvatar>();
    }
    if (_getIt.isRegistered<ChangePassword>()) {
      _getIt.unregister<ChangePassword>();
    }

    // 清理仓库
    if (_getIt.isRegistered<UserInfoRepository>()) {
      _getIt.unregister<UserInfoRepository>();
    }

    // 清理数据源
    if (_getIt.isRegistered<UserInfoRemoteDataSource>()) {
      _getIt.unregister<UserInfoRemoteDataSource>();
    }
  }
}
