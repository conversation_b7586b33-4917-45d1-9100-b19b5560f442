/// -----
/// student_grade_event.dart
///
/// 学生成绩BLoC事件，定义与学生成绩相关的所有事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 学生成绩事件基类
///
/// 所有与学生成绩相关的事件都继承自此类
abstract class StudentGradeEvent extends Equatable {
  const StudentGradeEvent();

  @override
  List<Object?> get props => [];
}

/// 加载学生成绩事件
///
/// 触发加载学生成绩数据的操作
class LoadStudentGradeEvent extends StudentGradeEvent {
  /// 实习计划ID
  final String planId;

  const LoadStudentGradeEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}

/// 刷新学生成绩事件
///
/// 触发刷新学生成绩数据的操作
class RefreshStudentGradeEvent extends StudentGradeEvent {
  /// 实习计划ID
  final String planId;

  const RefreshStudentGradeEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}
