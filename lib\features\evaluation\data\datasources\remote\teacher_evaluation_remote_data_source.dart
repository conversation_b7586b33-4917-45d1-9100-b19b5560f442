/// -----
/// teacher_evaluation_remote_data_source.dart
/// 
/// 老师评价远程数据源接口
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/features/evaluation/data/models/teacher_evaluation_model.dart';
import 'package:flutter_demo/features/evaluation/data/models/student_evaluate_score_request_model.dart';
import 'package:flutter_demo/features/internship/data/models/score_item_model.dart';
import 'package:flutter_demo/features/internship/data/models/score_submit_request_model.dart';

/// 老师评价远程数据源接口
/// 
/// 定义从远程API获取老师评价数据的操作
abstract class TeacherEvaluationRemoteDataSource {
  /// 获取可评价的老师列表
  ///
  /// [planId] 实习计划ID
  /// 返回老师评价模型列表
  /// 抛出 [ServerException] 当API调用失败时
  Future<List<TeacherEvaluationModel>> getTeacherList({
    required String planId,
  });

  /// 获取学生评价老师的评分项列表
  ///
  /// [planId] 实习计划ID
  /// [type] 评价类型，从上一个页面传过来的type
  /// 返回评分项模型列表
  /// 抛出 [ServerException] 当API调用失败时
  Future<List<ScoreItemModel>> getStudentEvaluationScoreItems({
    required String planId,
    required int type,
  });

  /// 提交学生评价老师的评分
  ///
  /// [request] 评分提交请求数据
  /// 抛出 [ServerException] 当API调用失败时
  Future<void> submitStudentEvaluationScore({
    required ScoreSubmitRequestModel request,
  });

  /// 提交学生评价老师的评分（新接口）
  ///
  /// [request] 学生评价老师评分请求数据
  /// 抛出 [ServerException] 当API调用失败时
  Future<void> submitStudentEvaluateScore({
    required StudentEvaluateScoreRequestModel request,
  });

  /// 获取评分详情（已评分）
  ///
  /// [recordId] 评分记录ID
  /// 返回评分项模型列表
  /// 抛出 [ServerException] 当API调用失败时
  Future<List<ScoreItemModel>> getScoreDetail({
    required String recordId,
  });
}
