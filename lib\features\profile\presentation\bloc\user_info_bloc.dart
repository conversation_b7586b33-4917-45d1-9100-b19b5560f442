/// -----
/// user_info_bloc.dart
/// 
/// 用户信息BLoC，管理用户信息的状态和业务逻辑
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/profile/domain/entities/user_info.dart';
import 'package:flutter_demo/features/profile/domain/usecases/change_password.dart';
import 'package:flutter_demo/features/profile/domain/usecases/get_user_info.dart';
import 'package:flutter_demo/features/profile/domain/usecases/save_user_avatar.dart';
import 'package:flutter_demo/features/profile/domain/usecases/update_user_gender.dart';
import 'package:flutter_demo/features/profile/presentation/bloc/user_info_event.dart';
import 'package:flutter_demo/features/profile/presentation/bloc/user_info_state.dart';

/// 用户信息BLoC
///
/// 管理用户信息的状态和业务逻辑
/// 处理获取用户信息、刷新用户信息、更新性别等事件
class UserInfoBloc extends Bloc<UserInfoEvent, UserInfoState> {
  final GetUserInfo _getUserInfo;
  final UpdateUserGender _updateUserGender;
  final SaveUserAvatar _saveUserAvatar;
  final ChangePassword _changePassword;

  static const String _tag = 'UserInfoBloc';

  /// 当前用户信息缓存，用于性别更新后的本地状态更新
  UserInfo? _currentUserInfo;

  UserInfoBloc({
    required GetUserInfo getUserInfo,
    required UpdateUserGender updateUserGender,
    required SaveUserAvatar saveUserAvatar,
    required ChangePassword changePassword,
  })  : _getUserInfo = getUserInfo,
        _updateUserGender = updateUserGender,
        _saveUserAvatar = saveUserAvatar,
        _changePassword = changePassword,
        super(const UserInfoInitial()) {
    // 注册事件处理器
    on<GetUserInfoEvent>(_onGetUserInfo);
    on<RefreshUserInfoEvent>(_onRefreshUserInfo);
    on<UpdateUserGenderEvent>(_onUpdateUserGender);
    on<SaveUserAvatarEvent>(_onSaveUserAvatar);
    on<ChangePasswordEvent>(_onChangePassword);
    on<ResetUserInfoEvent>(_onResetUserInfo);
  }

  /// 处理获取用户信息事件
  ///
  /// @param event 获取用户信息事件
  /// @param emit 状态发射器
  Future<void> _onGetUserInfo(
    GetUserInfoEvent event,
    Emitter<UserInfoState> emit,
  ) async {
    Logger.info(_tag, '处理获取用户信息事件');
    
    // 发射加载中状态
    emit(const UserInfoLoading());

    try {
      // 调用用例获取用户信息
      final result = await _getUserInfo(NoParams());

      // 根据结果发射相应状态
      result.fold(
        (failure) {
          Logger.error(_tag, '获取用户信息失败: ${failure.message}');
          emit(UserInfoError(failure.message));
        },
        (userInfo) {
          Logger.info(_tag, '获取用户信息成功: ${userInfo.userName}');
          // 缓存当前用户信息
          _currentUserInfo = userInfo;
          emit(UserInfoLoaded(userInfo));
        },
      );
    } catch (e) {
      Logger.error(_tag, '获取用户信息异常: $e');
      emit(UserInfoError('获取用户信息失败: $e'));
    }
  }

  /// 处理刷新用户信息事件
  ///
  /// @param event 刷新用户信息事件
  /// @param emit 状态发射器
  Future<void> _onRefreshUserInfo(
    RefreshUserInfoEvent event,
    Emitter<UserInfoState> emit,
  ) async {
    Logger.info(_tag, '处理刷新用户信息事件');

    // 刷新操作与获取操作相同
    await _onGetUserInfo(const GetUserInfoEvent(), emit);
  }

  /// 处理更新用户性别事件
  ///
  /// @param event 更新用户性别事件
  /// @param emit 状态发射器
  Future<void> _onUpdateUserGender(
    UpdateUserGenderEvent event,
    Emitter<UserInfoState> emit,
  ) async {
    Logger.info(_tag, '处理更新用户性别事件: ${event.gender}');

    // 发射更新中状态
    emit(const UserGenderUpdating());

    try {
      // 调用用例更新用户性别
      final result = await _updateUserGender(
        UpdateUserGenderParams(gender: event.gender),
      );

      // 根据结果发射相应状态
      result.fold(
        (failure) {
          Logger.error(_tag, '更新用户性别失败: ${failure.message}');
          emit(UserGenderUpdateError(failure.message));
        },
        (success) {
          Logger.info(_tag, '更新用户性别成功');

          // 如果有缓存的用户信息，更新性别字段（但不发射新状态）
          if (_currentUserInfo != null) {
            Logger.info(_tag, '有缓存用户信息，更新本地性别字段');
            final updatedUserInfo = _currentUserInfo!.copyWith(gender: event.gender);
            _currentUserInfo = updatedUserInfo;
            Logger.info(_tag, '本地性别字段已更新为: ${event.gender}');
          } else {
            Logger.warning(_tag, '没有缓存的用户信息，无法更新本地状态');
          }

          // 发射成功状态，让UI处理
          emit(const UserGenderUpdateSuccess());
        },
      );
    } catch (e) {
      Logger.error(_tag, '更新用户性别异常: $e');
      emit(UserGenderUpdateError('更新用户性别失败: $e'));
    }
  }

  /// 处理保存用户头像事件
  ///
  /// @param event 保存用户头像事件
  /// @param emit 状态发射器
  Future<void> _onSaveUserAvatar(
    SaveUserAvatarEvent event,
    Emitter<UserInfoState> emit,
  ) async {
    Logger.info(_tag, '处理保存用户头像事件: ${event.avatar}');

    // 发射保存中状态
    emit(const UserAvatarSaving());

    try {
      // 调用用例保存用户头像
      final result = await _saveUserAvatar(
        SaveUserAvatarParams(avatar: event.avatar),
      );

      // 根据结果发射相应状态
      result.fold(
        (failure) {
          Logger.error(_tag, '保存用户头像失败: ${failure.message}');
          emit(UserAvatarSaveError(failure.message));
        },
        (success) {
          Logger.info(_tag, '保存用户头像成功');

          // 如果有缓存的用户信息，更新头像字段
          if (_currentUserInfo != null) {
            Logger.info(_tag, '有缓存用户信息，更新本地头像字段');
            final updatedUserInfo = _currentUserInfo!.copyWith(avatar: event.avatar);
            _currentUserInfo = updatedUserInfo;
            Logger.info(_tag, '本地头像字段已更新为: ${event.avatar}');
          } else {
            Logger.warning(_tag, '没有缓存的用户信息，无法更新本地状态');
          }

          // 发射成功状态，让UI处理
          emit(const UserAvatarSaveSuccess());
        },
      );
    } catch (e) {
      Logger.error(_tag, '保存用户头像异常: $e');
      emit(UserAvatarSaveError('保存用户头像失败: $e'));
    }
  }

  /// 处理修改用户密码事件
  ///
  /// @param event 修改用户密码事件
  /// @param emit 状态发射器
  Future<void> _onChangePassword(
    ChangePasswordEvent event,
    Emitter<UserInfoState> emit,
  ) async {
    Logger.info(_tag, '处理修改用户密码事件');

    // 发射修改中状态
    emit(const UserPasswordChanging());

    try {
      // 调用用例修改用户密码
      final result = await _changePassword(
        ChangePasswordParams(
          oldPassword: event.oldPassword,
          newPassword: event.newPassword,
        ),
      );

      // 根据结果发射相应状态
      result.fold(
        (failure) {
          Logger.error(_tag, '修改用户密码失败: ${failure.message}');
          emit(UserPasswordChangeError(failure.message));
        },
        (success) {
          Logger.info(_tag, '修改用户密码成功');
          // 发射成功状态，让UI处理
          emit(const UserPasswordChangeSuccess());
        },
      );
    } catch (e) {
      Logger.error(_tag, '修改用户密码异常: $e');
      emit(UserPasswordChangeError('修改用户密码失败: $e'));
    }
  }

  /// 处理重置用户信息事件
  ///
  /// @param event 重置用户信息事件
  /// @param emit 状态发射器
  Future<void> _onResetUserInfo(
    ResetUserInfoEvent event,
    Emitter<UserInfoState> emit,
  ) async {
    Logger.info(_tag, '处理重置用户信息事件');

    try {
      // 清空缓存的用户信息
      _currentUserInfo = null;

      // 发射初始状态，重置BLoC状态
      emit(const UserInfoInitial());

      Logger.info(_tag, '用户信息状态已重置');
    } catch (e) {
      Logger.error(_tag, '重置用户信息异常: $e');
      // 即使重置失败，也要发射初始状态
      emit(const UserInfoInitial());
    }
  }
}
