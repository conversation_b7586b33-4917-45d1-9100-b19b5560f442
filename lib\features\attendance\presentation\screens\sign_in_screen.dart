/// -----
/// sign_in_screen.dart
///
/// 签到页面，用于学生进行日常签到打卡
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/features/attendance/presentation/screens/location_adjust_screen.dart';
import 'package:flutter_demo/features/attendance/presentation/widgets/sign_location_row_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:async';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/features/attendance/presentation/screens/attendance_calendar_screen.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/core/utils/image_upload_util.dart';
import 'package:flutter_demo/features/attendance/presentation/bloc/sign_in/sign_in_bloc.dart';
import 'package:flutter_demo/features/attendance/presentation/bloc/sign_in/sign_in_event.dart';
import 'package:flutter_demo/features/attendance/presentation/bloc/sign_in/sign_in_state.dart';
import 'package:flutter_demo/features/attendance/data/models/location_data_model.dart';
import 'package:flutter_demo/core/services/baidu_location_service.dart';
import 'package:flutter_demo/core/models/location_state.dart';

/// 签到页面
class SignInScreen extends StatefulWidget {
  const SignInScreen({Key? key}) : super(key: key);

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  File? _selectedImage; // 只能上传1张图片
  String? _uploadedImageUrl; // 上传成功后的图片URL
  bool _isUploading = false; // 上传状态

  bool _isSigningIn = false;

  // 定位服务
  late BaiduLocationService _locationService;
  StreamSubscription<LocationState>? _locationSubscription;

  // 当前定位状态
  LocationState _locationState = const LocationState.initial();

  // 防抖定时器，避免快速重复初始化
  Timer? _initLocationTimer;
  bool _isInitializingLocation = false;

  // 时间相关
  String _currentTime = '';
  Timer? _timer;

  // 全局实习计划BLoC
  late PlanListGlobalBloc _planListGlobalBloc;

  // 签到BLoC
  late SignInBloc _signInBloc;

  @override
  void initState() {
    super.initState();
    debugPrint('📱 [签到页面] initState 开始...');
    _initializeTime();
    _initializePlanListBloc();
    _initializeSignInBloc();
    _initializeLocationService();
    debugPrint('📱 [签到页面] initState 完成');
  }

  @override
  void dispose() {
    debugPrint('📱 [签到页面] dispose 开始...');
    _timer?.cancel();
    _initLocationTimer?.cancel();
    _locationSubscription?.cancel();
    // 不要销毁定位服务，让其他页面可以继续使用
    // 只有在应用退出时才应该销毁定位服务
    debugPrint('📱 [签到页面] 保持定位服务运行，供其他页面使用');
    _signInBloc.close();
    debugPrint('📱 [签到页面] dispose 完成');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: CustomAppBar(
        title: '签到',
        actions: [
          TextButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AttendanceCalendarScreen(),
                ),
              );
            },
            child: Text(
              '历史记录',
              style: TextStyle(
                color: const Color(0xFF2165F6),
                fontSize: 28.sp,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 学期选择区域
          const CourseHeaderSection(),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 20.h),

                  // 附件上传区域
                  _buildFileUploadSection(),

                  SizedBox(height: 20.h),

                  // 位置信息区域
                  _buildLocationSection(),

                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建附件上传区域
  Widget _buildFileUploadSection() {
    return Container(
      padding: EdgeInsets.all(25.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '附件上传',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 20.h),
          _buildFileGrid(),
        ],
      ),
    );
  }

  /// 构建文件网格
  Widget _buildFileGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      mainAxisSpacing: 20.w,
      crossAxisSpacing: 20.w,
      childAspectRatio: 1,
      children: [
        // 如果有选中的图片，显示图片预览
        if (_selectedImage != null)
          _buildImageItem(_selectedImage!),
        // 如果没有选中图片且可以添加，显示添加按钮
        if (_selectedImage == null)
          _buildAddImageButton(),
      ],
    );
  }

  /// 构建图片项
  Widget _buildImageItem(File file) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            image: DecorationImage(
              image: FileImage(file),
              fit: BoxFit.cover,
            ),
          ),
        ),
        // 上传状态指示器
        if (_isUploading)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: Colors.black54,
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 40.w,
                      height: 40.w,
                      child: const CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 3,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      '上传中...',
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        // 删除按钮
        if (!_isUploading)
          Positioned(
            top: -10.w,
            right: -10.w,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedImage = null;
                  _uploadedImageUrl = null;
                });
              },
              child: Container(
                padding: EdgeInsets.all(4.w),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  size: 32.w,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// 构建添加图片按钮
  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Center(
          child: Icon(
            Icons.add,
            size: 80.w,
            color: const Color(0xFF999999),
          ),
        ),
      ),
    );
  }

  /// 构建位置信息区域
  Widget _buildLocationSection() {
    return Container(
      padding: EdgeInsets.all(20.w),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: SignLocationRowWidget(
                  address: '当前地址:  ${_locationState.location}',
                  onAdjustLocation: _adjustLocation,
                ),
              ),
              // 显示定位状态指示器
              if (_locationState.isLoading) ...[
                SizedBox(width: 8.w),
                SizedBox(
                  width: 16.w,
                  height: 16.w,
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                ),
              ],
            ],
          ),
          // 显示定位错误信息
          if (_locationState.hasError) ...[
            SizedBox(height: 8.h),
            Row(
              children: [
                Icon(
                  Icons.error_outline,
                  size: 16.w,
                  color: Colors.red,
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Text(
                    _locationState.error!,
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: Colors.red,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: _refreshLocation,
                  child: Text(
                    '重试',
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ],
          SizedBox(height: 50.h),
          // 签到按钮
          _buildSignInButton(),
          SizedBox(height: 40.h),
        ],
      ),
    );
  }

  /// 构建签到按钮
  Widget _buildSignInButton() {
    return Center(
      child: GestureDetector(
        onTap: _isSigningIn ? null : _performSignIn,
        child: Container(
          width: 200.w,
          height: 200.w,
          decoration: const BoxDecoration(
            color: Color(0xFF2165F6),
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isSigningIn)
                SizedBox(
                  width: 40.w,
                  height: 40.w,
                  child: const CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                )
              else ...[
                Text(
                  '签到',
                  style: TextStyle(
                    fontSize: 36.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  _currentTime,
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: Colors.white,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _selectedImage = File(pickedFile.path);
          _uploadedImageUrl = null; // 重置上传URL
        });

        // 自动上传图片
        await _uploadImage();
      }
    } on Exception catch (e) {
      debugPrint('Error picking image: $e');
      if (mounted) {
        AppSnackBar.showError(context, '选择图片失败，请重试');
      }
    }
  }

  /// 上传图片
  Future<void> _uploadImage() async {
    if (_selectedImage == null) return;

    setState(() {
      _isUploading = true;
    });

    try {
      final imageUploadUtil = ImageUploadUtil.instance;
      final result = await imageUploadUtil.uploadSignInImage(
        filePath: _selectedImage!.path,
        onProgress: (progress) {
          // 可以在这里更新上传进度
          debugPrint('上传进度: ${(progress * 100).toInt()}%');
        },
      );

      setState(() {
        _uploadedImageUrl = result.fileUrl;
        _isUploading = false;
      });

      if (mounted) {
        AppSnackBar.showSuccess(context, '图片上传成功');
      }
    } on Exception catch (e) {
      setState(() {
        _isUploading = false;
      });

      debugPrint('Error uploading image: $e');
      if (mounted) {
        AppSnackBar.showError(context, '图片上传失败: $e');
      }
    }
  }

  /// 调整位置
  Future<void> _adjustLocation() async {
    // 检查是否有当前位置信息
    if (!_locationState.hasCompleteLocation) {
      AppSnackBar.showError(context, '请等待定位完成后再调整位置');
      return;
    }

    // 创建当前位置数据
    final currentLocation = LocationDataModel(
      latitude: double.parse(_locationState.latitude!),
      longitude: double.parse(_locationState.longitude!),
      address: _locationState.location,
      province: _locationState.province,
      city: _locationState.city,
    );

    // 跳转到位置调整页面，传递当前位置信息
    final result = await Navigator.push<LocationDataModel>(
      context,
      MaterialPageRoute(
        builder: (context) => LocationAdjustScreen(
          currentLocation: currentLocation,
        ),
      ),
    );

    if (result != null && mounted) {
      setState(() {
        _locationState = _locationState.copyWith(
          location: result.address,
          latitude: result.latitude.toString(),
          longitude: result.longitude.toString(),
          province: result.province ?? _locationState.province,
          city: result.city ?? _locationState.city,
        );
      });

      debugPrint('🔄 [位置调整] 更新位置信息');
      debugPrint('🔄 [位置调整] 地址: ${result.address}');
      debugPrint('🔄 [位置调整] 纬度: ${result.latitude}, 经度: ${result.longitude}');
      debugPrint('🔄 [位置调整] 省份: ${result.province}, 城市: ${result.city}');
    }
  }

  /// 执行签到
  Future<void> _performSignIn() async {
    // 检查定位信息是否可用
    if (!_locationState.hasCompleteLocation) {
      AppSnackBar.showError(context, '定位信息不完整，请等待定位完成后再试');
      return;
    }

    // 检查是否有当前选中的实习计划
    final globalState = _planListGlobalBloc.state;
    String? currentPlanId;

    if (globalState is PlanListGlobalLoadedState && globalState.currentPlanId != null) {
      currentPlanId = globalState.currentPlanId!;
    }

    if (currentPlanId == null || currentPlanId.isEmpty) {
      AppSnackBar.showError(context, '未找到当前实习计划，请先选择实习计划');
      return;
    }

    debugPrint('🔥 [签到] 开始签到流程');
    debugPrint('🔥 [签到] 位置: ${_locationState.location}');
    debugPrint('🔥 [签到] 纬度: ${_locationState.latitude}, 经度: ${_locationState.longitude}');
    debugPrint('🔥 [签到] 省份: ${_locationState.province}, 城市: ${_locationState.city}');
    debugPrint('🔥 [签到] 实习计划ID: $currentPlanId');
    debugPrint('🔥 [签到] 附件URL: $_uploadedImageUrl');

    // 触发签到事件
    _signInBloc.add(PerformSignInEvent(
      location: _locationState.location,
      latitude: _locationState.latitude!,
      longitude: _locationState.longitude!,
      province: _locationState.province!,
      city: _locationState.city!,
      planId: currentPlanId,
      fileUrl: _uploadedImageUrl,
    ));
  }

  /// 初始化定位服务
  Future<void> _initializeLocationService() async {
    debugPrint('📱 [签到页面] 开始初始化定位服务...');

    // 防抖处理：如果正在初始化，取消之前的定时器
    if (_isInitializingLocation) {
      debugPrint('📱 [签到页面] 正在初始化中，忽略重复请求');
      return;
    }

    _initLocationTimer?.cancel();
    _isInitializingLocation = true;

    try {
      // 先取消之前的监听
      await _locationSubscription?.cancel();
      _locationSubscription = null;

      // 获取定位服务实例
      _locationService = BaiduLocationService.instance;

      // 先停止当前定位，确保状态干净
      await _locationService.stopLocation();

      // 短暂延迟，确保停止操作完成
      await Future.delayed(const Duration(milliseconds: 300));

      // 重新监听定位状态变化
      _locationSubscription = _locationService.locationStream.listen((state) {
        debugPrint('📱 [签到页面] 收到定位状态变化: ${state.status}, 位置: ${state.location}');
        if (mounted) {
          setState(() {
            _locationState = state;
          });
        }
      });

      // 检查当前定位服务状态
      final currentState = _locationService.currentState;
      debugPrint('📱 [签到页面] 当前定位状态: ${currentState.status}');

      // 关键修复：如果当前状态是success，说明可能是从其他页面返回
      // 这种情况下强制重启定位服务，避免OkHttp兼容性问题
      bool shouldForceRestart = currentState.status == LocationStatus.success;

      if (shouldForceRestart) {
        debugPrint('📱 [签到页面] 检测到从其他页面返回，强制重启定位服务避免兼容性问题');
        // 重新获取实例（因为restart会销毁当前实例）
        await BaiduLocationService.restart();
        _locationService = BaiduLocationService.instance;

        // 重新监听（因为实例已更换）
        await _locationSubscription?.cancel();
        _locationSubscription = _locationService.locationStream.listen((state) {
          debugPrint('📱 [签到页面] 收到定位状态变化: ${state.status}, 位置: ${state.location}');
          if (mounted) {
            setState(() {
              _locationState = state;
            });
          }
        });

        // 延迟更长时间确保重启完成
        await Future.delayed(const Duration(milliseconds: 800));
      }

      // 统一使用刷新定位的方式
      debugPrint('📱 [签到页面] 开始刷新定位...');

      // 使用防抖定时器延迟执行定位
      _initLocationTimer = Timer(const Duration(milliseconds: 500), () async {
        if (mounted && _isInitializingLocation) {
          try {
            if (shouldForceRestart) {
              // 如果是强制重启，使用initialize而不是refreshLocation
              await _locationService.initialize();
              debugPrint('📱 [签到页面] 定位服务重新初始化请求已发送');
            } else {
              await _locationService.refreshLocation();
              debugPrint('📱 [签到页面] 定位刷新请求已发送');
            }
          } catch (e) {
            debugPrint('❌ [签到页面] 定位操作失败: $e');
            if (mounted) {
              setState(() {
                _locationState = LocationState.error(
                  error: '定位操作失败: $e',
                  location: '定位失败',
                );
              });
            }
          }
        }
      });

    } catch (e) {
      debugPrint('❌ [签到页面] 初始化定位服务失败: $e');
      if (mounted) {
        setState(() {
          _locationState = LocationState.error(
            error: '初始化定位服务失败: $e',
            location: '定位服务异常',
          );
        });
      }
    } finally {
      // 延迟重置初始化标志，避免过快的重复调用
      Timer(const Duration(seconds: 2), () {
        _isInitializingLocation = false;
      });
    }
  }

  /// 刷新定位
  Future<void> _refreshLocation() async {
    debugPrint('📱 [签到页面] 手动刷新定位...');

    // 如果正在初始化，先等待完成
    if (_isInitializingLocation) {
      debugPrint('📱 [签到页面] 正在初始化中，等待完成后再刷新');
      // 等待初始化完成
      int waitCount = 0;
      while (_isInitializingLocation && waitCount < 10) {
        await Future.delayed(const Duration(milliseconds: 200));
        waitCount++;
      }
    }

    try {
      await _locationService.refreshLocation();
      debugPrint('📱 [签到页面] 手动刷新定位请求已发送');
    } catch (e) {
      debugPrint('❌ [签到页面] 手动刷新定位失败: $e');
      if (mounted) {
        setState(() {
          _locationState = LocationState.error(
            error: '刷新定位失败: $e',
            location: '定位刷新异常',
          );
        });
      }
    }
  }

  /// 初始化时间显示
  void _initializeTime() {
    // 立即更新一次时间
    _updateCurrentTime();

    // 启动定时器，每秒更新一次时间
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateCurrentTime();
    });
  }

  /// 更新当前时间
  void _updateCurrentTime() {
    final now = DateTime.now();
    final timeString = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

    if (mounted) {
      setState(() {
        _currentTime = timeString;
      });
    }
  }

  /// 初始化全局实习计划BLoC
  void _initializePlanListBloc() {
    _planListGlobalBloc = GetIt.instance<PlanListGlobalBloc>();

    // 如果还没有加载数据，触发加载
    if (_planListGlobalBloc.state is! PlanListGlobalLoadedState) {
      _planListGlobalBloc.add(const LoadPlanListGlobalEvent());
    }
  }

  /// 初始化签到BLoC
  void _initializeSignInBloc() {
    _signInBloc = GetIt.instance<SignInBloc>();

    // 监听签到状态变化
    _signInBloc.stream.listen((state) {
      if (!mounted) return;

      if (state is SignInLoadingState) {
        setState(() {
          _isSigningIn = true;
        });
      } else if (state is SignInSuccessState) {
        setState(() {
          _isSigningIn = false;
        });
        AppSnackBar.showSuccess(context, state.message);
      } else if (state is SignInFailureState) {
        setState(() {
          _isSigningIn = false;
        });
        AppSnackBar.showError(context, state.message);
      }
    });
  }
}
