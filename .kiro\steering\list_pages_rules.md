---
inclusion: fileMatch
fileMatchPattern: '**/presentation/pages/**list**'
---

# 列表页面规则

当编辑包含"list"的页面文件时激活

## 推荐第三方库
- **下拉刷新和上拉加载**: `pull_to_refresh` 或 `flutter_easyrefresh`
- **滚动检测**: `visibility_detector` 或 `flutter_visibility_detector`
- **滚动控制**: `scrollable_positioned_list` 用于滚动到特定位置

## BLoC 状态管理集成规则

### 事件定义规则
必须包含以下事件：
- `LoadXxxListEvent`: 初始加载列表
- `RefreshXxxListEvent`: 下拉刷新列表
- `LoadMoreXxxListEvent`: 上拉加载更多

### 状态定义规则
必须包含以下状态：
- `XxxListInitial`: 初始状态
- `XxxListLoading`: 加载状态（区分首次加载和加载更多）
- `XxxListSuccess`: 成功状态（包含数据和是否到达末页标识）
- `XxxListFailure`: 失败状态（包含错误信息和已有数据）
- `XxxListRefreshSuccess`: 刷新成功状态
- `XxxListRefreshFailure`: 刷新失败状态
- `XxxListLoadMoreSuccess`: 加载更多成功状态
- `XxxListLoadMoreFailure`: 加载更多失败状态

### BLoC 实现规则
- 在 BLoC 中维护当前页码和页面大小
- 使用 `_hasReachedMax` 标识是否到达末页
- 处理初始加载、下拉刷新、上拉加载更多三种场景

## 加载状态的UI展示规则
1. **初始加载**：显示居中的加载指示器
2. **下拉刷新**：显示下拉刷新指示器（如水滴效果）
3. **上拉加载更多**：显示底部加载指示器
4. **空数据状态**：显示空数据缺省页
5. **错误状态**：显示错误缺省页，并提供重试按钮

## 分页逻辑处理规则
1. **页码管理**：在 BLoC 中维护当前页码和页面大小
2. **初始加载**：加载第一页数据
3. **下拉刷新**：重置页码为1，重新加载第一页数据
4. **上拉加载更多**：页码加1，加载下一页数据
5. **判断是否到达末页**：根据返回数据量是否小于页面大小来判断

## 错误处理机制规则
1. **网络错误**：显示网络错误缺省页，提供重试按钮
2. **服务器错误**：显示服务器错误缺省页，提供重试按钮
3. **刷新失败**：显示刷新失败提示，保留原有数据
4. **加载更多失败**：显示加载更多失败提示，保留已加载数据，提供重试选项

## 性能优化规则
1. **列表项缓存**：使用 `ListView.builder` 而非 `ListView`
2. **懒加载图片**：使用 `CachedNetworkImage` 加载网络图片
3. **防抖动处理**：避免短时间内多次触发加载更多
4. **滚动优化**：使用 `const` 构造函数和 `RepaintBoundary` 减少重绘

## 用例实现规则
- 创建专门的用例类处理列表数据获取
- 用例参数应包含页码和页面大小
- 返回 Either<Failure, List<T>> 类型

## 仓库实现规则
- 在仓库中检查网络状态
- 处理网络异常和服务器异常
- 将异常转换为相应的失败类型

## 数据源实现规则
- 使用 `DioClient` 发送网络请求
- 处理分页参数
- 将响应数据转换为模型列表