/// -----
/// student_grade.dart
///
/// 学生成绩领域实体，表示学生的成绩信息
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 学生成绩实体
///
/// 包含总分和评分项列表
class StudentGrade extends Equatable {
  /// 评分项列表
  final List<GradeItem> items;
  
  /// 总分
  final int score;

  const StudentGrade({
    required this.items,
    required this.score,
  });

  @override
  List<Object?> get props => [items, score];

  /// 根据类型获取评分项
  GradeItem? getItemByType(int type) {
    try {
      return items.firstWhere((item) => item.type == type);
    } catch (e) {
      return null;
    }
  }

  /// 获取自我评分
  GradeItem? get selfEvaluation => getItemByType(0);

  /// 获取班主任评价学生
  GradeItem? get teacherEvaluation => getItemByType(4);

  /// 获取指导老师评价学生
  GradeItem? get mentorEvaluation => getItemByType(5);

  /// 获取企业老师评价学生
  GradeItem? get companyEvaluation => getItemByType(6);
}

/// 成绩项实体
///
/// 表示一个评分项的数据
class GradeItem extends Equatable {
  /// 评分ID
  final String recordId;

  /// 分数
  final int score;

  /// 评价类型
  /// 0:自我评分，1:学生评价班主任老师，2:学生评价指导老师，3:学生评价企业老师，
  /// 4:班主任评价学生，5:指导老师评价学生，6:企业老师评价学生
  final int type;

  /// 评价类型名称（由API返回）
  final String typeName;

  const GradeItem({
    required this.recordId,
    required this.score,
    required this.type,
    required this.typeName,
  });

  @override
  List<Object?> get props => [recordId, score, type, typeName];

  /// 获取显示在成绩页面的标题
  /// 现在直接使用API返回的typeName字段
  String get displayTitle => typeName;

  /// 是否有分数（分数大于0表示已评分）
  bool get hasScore => score > 0;
}
