Add-Type -AssemblyName System.Drawing

$sourceImagePath = "web\ic_launcher.jpg"
$androidResPath = "android\app\src\main\res"

$densities = @(
    @{name="mipmap-mdpi"; size=48},
    @{name="mipmap-hdpi"; size=72},
    @{name="mipmap-xhdpi"; size=96},
    @{name="mipmap-xxhdpi"; size=144},
    @{name="mipmap-xxxhdpi"; size=192}
)

if (-not (Test-Path $sourceImagePath)) {
    Write-Host "Source file not found: $sourceImagePath"
    exit 1
}

Write-Host "Processing icon file: $sourceImagePath"

try {
    $sourceImage = [System.Drawing.Image]::FromFile((Resolve-Path $sourceImagePath).Path)
    
    Write-Host "Source image size: $($sourceImage.Width) x $($sourceImage.Height)"
    
    foreach ($density in $densities) {
        $targetSize = $density.size
        $densityName = $density.name
        $targetDir = Join-Path $androidResPath $densityName
        $targetFile = Join-Path $targetDir "ic_launcher.png"
        
        Write-Host "Creating $densityName icon ($targetSize x $targetSize)..."
        
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        $resizedImage = New-Object System.Drawing.Bitmap($targetSize, $targetSize)
        $graphics = [System.Drawing.Graphics]::FromImage($resizedImage)
        
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
        $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        
        $graphics.DrawImage($sourceImage, 0, 0, $targetSize, $targetSize)
        
        $resizedImage.Save($targetFile, [System.Drawing.Imaging.ImageFormat]::Png)
        
        $graphics.Dispose()
        $resizedImage.Dispose()
        
        Write-Host "Saved: $targetFile"
    }
    
    $sourceImage.Dispose()
    
    Write-Host "Icon processing completed! Generated files:"
    foreach ($density in $densities) {
        $targetSize = $density.size
        $densityName = $density.name
        $targetFile = Join-Path (Join-Path $androidResPath $densityName) "ic_launcher.png"
        Write-Host "  $targetFile ($targetSize x $targetSize)"
    }
    
} catch {
    Write-Host "Error processing image: $($_.Exception.Message)"
    exit 1
}

Write-Host "All icons generated successfully!"
