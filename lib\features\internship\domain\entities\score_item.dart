/// -----
/// score_item.dart
/// 
/// 评分项实体类，表示单个评分项的数据结构
///
/// <AUTHOR>
/// @date 2025-07-16
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 评分项实体类
///
/// 表示单个评分项的数据结构，包含标题、内容描述和分数
class ScoreItem extends Equatable {
  /// 评分项标题
  final String title;
  
  /// 评分项内容描述
  final String content;
  
  /// 评分分数，null表示未评分
  final int? score;

  const ScoreItem({
    required this.title,
    required this.content,
    this.score,
  });

  /// 创建副本并更新指定字段
  ScoreItem copyWith({
    String? title,
    String? content,
    int? score,
  }) {
    return ScoreItem(
      title: title ?? this.title,
      content: content ?? this.content,
      score: score ?? this.score,
    );
  }

  @override
  List<Object?> get props => [title, content, score];

  @override
  String toString() {
    return 'ScoreItem(title: $title, content: $content, score: $score)';
  }
}
