/// -----
/// student_grade_state.dart
///
/// 学生成绩BLoC状态，定义与学生成绩相关的所有状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/grade/domain/entities/student_grade.dart';

/// 学生成绩状态基类
///
/// 所有与学生成绩相关的状态都继承自此类
abstract class StudentGradeState extends Equatable {
  const StudentGradeState();

  @override
  List<Object?> get props => [];
}

/// 学生成绩初始状态
///
/// 表示学生成绩数据尚未加载的状态
class StudentGradeInitial extends StudentGradeState {
  const StudentGradeInitial();
}

/// 学生成绩加载中状态
///
/// 表示正在加载学生成绩数据的状态
class StudentGradeLoading extends StudentGradeState {
  const StudentGradeLoading();
}

/// 学生成绩刷新中状态
///
/// 表示正在刷新学生成绩数据的状态
class StudentGradeRefreshing extends StudentGradeState {
  /// 之前的学生成绩数据
  final StudentGrade previousGrade;
  
  /// 实习计划ID
  final String planId;

  const StudentGradeRefreshing({
    required this.previousGrade,
    required this.planId,
  });

  @override
  List<Object?> get props => [previousGrade, planId];
}

/// 学生成绩加载成功状态
///
/// 表示成功加载学生成绩数据的状态
class StudentGradeLoaded extends StudentGradeState {
  /// 学生成绩数据
  final StudentGrade grade;
  
  /// 实习计划ID
  final String planId;
  
  /// 课程名称
  final String courseName;

  const StudentGradeLoaded({
    required this.grade,
    required this.planId,
    required this.courseName,
  });

  @override
  List<Object?> get props => [grade, planId, courseName];
}

/// 学生成绩加载失败状态
///
/// 表示加载学生成绩数据失败的状态
class StudentGradeError extends StudentGradeState {
  /// 错误信息
  final String message;
  
  /// 实习计划ID
  final String planId;

  const StudentGradeError({
    required this.message,
    required this.planId,
  });

  @override
  List<Object?> get props => [message, planId];
}

/// 学生成绩刷新失败状态
///
/// 表示刷新学生成绩数据失败的状态
class StudentGradeRefreshError extends StudentGradeState {
  /// 错误信息
  final String message;
  
  /// 之前的学生成绩数据
  final StudentGrade previousGrade;
  
  /// 实习计划ID
  final String planId;

  const StudentGradeRefreshError({
    required this.message,
    required this.previousGrade,
    required this.planId,
  });

  @override
  List<Object?> get props => [message, previousGrade, planId];
}

/// 学生成绩为空状态
///
/// 表示学生成绩数据为空的状态
class StudentGradeEmpty extends StudentGradeState {
  /// 实习计划ID
  final String planId;
  
  /// 课程名称
  final String courseName;

  const StudentGradeEmpty({
    required this.planId,
    required this.courseName,
  });

  @override
  List<Object?> get props => [planId, courseName];
}
