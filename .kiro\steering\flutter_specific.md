---
inclusion: fileMatch
fileMatchPattern: '*.dart'
---

# Flutter开发专用规则

当编辑.dart文件时，这个规则会自动激活

## Flutter代码规范

### Widget构建规范
- 优先使用const构造函数
- 合理使用StatelessWidget和StatefulWidget
- 遵循Flutter的生命周期管理
- 使用flutter_screenutil进行屏幕适配

### 状态管理
- 使用BLoC模式进行状态管理
- 避免在Widget中直接处理业务逻辑
- 合理使用Provider进行依赖注入
- 状态类必须继承Equatable

### 性能优化
- 避免不必要的Widget重建
- 使用ListView.builder处理长列表
- 合理使用缓存机制
- 使用RepaintBoundary减少重绘

### UI组件规范
- 统一使用CustomAppBar而不是标准AppBar
- 使用LoadingWidget显示加载状态
- 使用EmptyStateWidget显示缺省页
- 使用AppSnackBar显示提示信息

### 百度地图集成
- 使用flutter_bmflocation进行定位
- 使用flutter_baidu_mapapi_*系列包
- 正确处理地图权限请求