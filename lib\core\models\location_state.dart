/// -----
/// location_state.dart
///
/// 定位状态模型
/// 用于表示定位服务的当前状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 定位状态枚举
enum LocationStatus {
  /// 初始状态
  initial,
  /// 正在定位
  loading,
  /// 定位成功
  success,
  /// 定位失败
  error,
}

/// 定位状态模型
///
/// 包含定位的所有状态信息，用于UI层监听和响应
class LocationState extends Equatable {
  /// 定位状态
  final LocationStatus status;
  
  /// 当前位置描述
  final String location;
  
  /// 纬度
  final String? latitude;
  
  /// 经度
  final String? longitude;
  
  /// 省份
  final String? province;
  
  /// 城市
  final String? city;
  
  /// 错误信息
  final String? error;

  const LocationState({
    this.status = LocationStatus.initial,
    this.location = '正在获取位置信息...',
    this.latitude,
    this.longitude,
    this.province,
    this.city,
    this.error,
  });

  /// 创建初始状态
  const LocationState.initial() : this();

  /// 创建加载状态
  const LocationState.loading() : this(
    status: LocationStatus.loading,
    location: '正在获取位置信息...',
  );

  /// 创建成功状态
  const LocationState.success({
    required String location,
    required String latitude,
    required String longitude,
    String? province,
    String? city,
  }) : this(
    status: LocationStatus.success,
    location: location,
    latitude: latitude,
    longitude: longitude,
    province: province,
    city: city,
  );

  /// 创建错误状态
  const LocationState.error({
    required String error,
    String location = '定位失败',
  }) : this(
    status: LocationStatus.error,
    location: location,
    error: error,
  );

  /// 复制并更新部分属性
  LocationState copyWith({
    LocationStatus? status,
    String? location,
    String? latitude,
    String? longitude,
    String? province,
    String? city,
    String? error,
  }) {
    return LocationState(
      status: status ?? this.status,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      province: province ?? this.province,
      city: city ?? this.city,
      error: error ?? this.error,
    );
  }

  /// 是否正在加载
  bool get isLoading => status == LocationStatus.loading;

  /// 是否成功
  bool get isSuccess => status == LocationStatus.success;

  /// 是否有错误
  bool get hasError => status == LocationStatus.error;

  /// 是否有完整的位置信息
  bool get hasCompleteLocation => 
    latitude != null && 
    longitude != null && 
    province != null && 
    city != null;

  @override
  List<Object?> get props => [
    status,
    location,
    latitude,
    longitude,
    province,
    city,
    error,
  ];

  @override
  String toString() {
    return 'LocationState(status: $status, location: $location, latitude: $latitude, longitude: $longitude, province: $province, city: $city, error: $error)';
  }
}
