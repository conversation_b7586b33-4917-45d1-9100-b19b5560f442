/// -----
/// sign_in_record.dart
///
/// 签到记录实体
/// 领域层的签到记录数据结构
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 签到记录实体
///
/// 领域层的签到记录数据结构
class SignInRecord extends Equatable {
  /// 主键ID
  final String id;
  
  /// 学生ID
  final String studentId;
  
  /// 学生姓名
  final String studentName;
  
  /// 关联实习计划ID
  final String planId;
  
  /// 查询年
  final int year;
  
  /// 查询月份
  final int month;
  
  /// 查询日期
  final int day;
  
  /// 签到时间戳（毫秒）
  final int signTime;
  
  /// 签到纬度
  final String latitude;
  
  /// 签到经度
  final String longitude;
  
  /// 签到省
  final String province;
  
  /// 签到市
  final String city;
  
  /// 签到位置描述
  final String location;
  
  /// 签到设备信息（如手机型号、操作系统）
  final String deviceInfo;
  
  /// 签到附件
  final String? fileUrl;
  
  /// 签到状态（1=正常，2=跨省，3=跨市）
  final int status;
  
  /// 创建时间戳
  final int createTime;
  
  /// 更新时间戳
  final int? updateTime;

  const SignInRecord({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.planId,
    required this.year,
    required this.month,
    required this.day,
    required this.signTime,
    required this.latitude,
    required this.longitude,
    required this.province,
    required this.city,
    required this.location,
    required this.deviceInfo,
    this.fileUrl,
    required this.status,
    required this.createTime,
    this.updateTime,
  });

  /// 获取签到日期
  DateTime get signDate => DateTime(year, month, day);
  
  /// 获取签到时间
  DateTime get signDateTime => DateTime.fromMillisecondsSinceEpoch(signTime);
  
  /// 是否正常签到
  bool get isNormal => status == 1;
  
  /// 是否异常签到
  bool get isAbnormal => status != 1;

  /// 复制并更新部分属性
  SignInRecord copyWith({
    String? id,
    String? studentId,
    String? studentName,
    String? planId,
    int? year,
    int? month,
    int? day,
    int? signTime,
    String? latitude,
    String? longitude,
    String? province,
    String? city,
    String? location,
    String? deviceInfo,
    String? fileUrl,
    int? status,
    int? createTime,
    int? updateTime,
  }) {
    return SignInRecord(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      planId: planId ?? this.planId,
      year: year ?? this.year,
      month: month ?? this.month,
      day: day ?? this.day,
      signTime: signTime ?? this.signTime,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      province: province ?? this.province,
      city: city ?? this.city,
      location: location ?? this.location,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      fileUrl: fileUrl ?? this.fileUrl,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  List<Object?> get props => [
    id,
    studentId,
    studentName,
    planId,
    year,
    month,
    day,
    signTime,
    latitude,
    longitude,
    province,
    city,
    location,
    deviceInfo,
    fileUrl,
    status,
    createTime,
    updateTime,
  ];

  @override
  String toString() {
    return 'SignInRecord(id: $id, studentId: $studentId, studentName: $studentName, planId: $planId, year: $year, month: $month, day: $day, signTime: $signTime, latitude: $latitude, longitude: $longitude, province: $province, city: $city, location: $location, deviceInfo: $deviceInfo, fileUrl: $fileUrl, status: $status, createTime: $createTime, updateTime: $updateTime)';
  }
}
