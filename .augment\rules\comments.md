---
type: "always_apply"
---

# 注释规则

## 文件注释规则
- 每个新创建的文件需要添加文件注释，说明该文件的主要功能和用途
- 文件注释应放在文件的最开头，使用相应语言的注释标记
- 所有文件必须包含作者信息：Mr.Wang
- 所有文件必须包含版权声明：Copyright © 2025
- 文件头注释模板应参考以下格式（根据不同语言的注释格式进行调整）：

```dart
/// -----
/// [文件名]
/// 
/// [文件功能描述]
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----
```

## 类注释规则
- 每个类都应该有清晰的注释说明其用途和职责
- 类注释应包含类的主要功能、使用场景和重要说明
- 对于复杂的类，应该说明其设计模式和架构考虑

## 方法注释规则
- 公共方法必须添加注释，说明方法的功能、参数和返回值
- 复杂的私有方法也应该添加注释
- 方法注释应包含：
  - 方法功能描述
  - 参数说明（如果有）
  - 返回值说明（如果有）
  - 异常说明（如果会抛出异常）
  - 使用示例（对于复杂方法）

## 代码内注释规则
- 复杂的业务逻辑应该添加行内注释
- 重要的算法步骤应该添加说明
- 临时解决方案或待优化的代码应该添加 TODO 注释
- 已知问题或限制应该添加 FIXME 注释

## 注释质量要求
- 注释应该简洁明了，避免冗余
- 注释应该与代码保持同步，及时更新
- 避免无意义的注释，如 `// 设置变量 x = 1`
- 使用中文注释，确保团队成员都能理解

## 文档注释规则
- 对于对外提供的API，应该使用文档注释格式
- 文档注释应该包含完整的使用说明和示例
- 重要的配置类和工具类应该有详细的文档注释
