/// -----
/// student_grade_bloc.dart
///
/// 学生成绩BLoC，处理学生成绩相关的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/features/grade/domain/usecases/get_student_grade_list_usecase.dart';
import 'package:flutter_demo/core/error/failures/failures.dart';
import 'student_grade_event.dart';
import 'student_grade_state.dart';

/// 学生成绩BLoC
///
/// 处理学生成绩相关的业务逻辑
class StudentGradeBloc extends Bloc<StudentGradeEvent, StudentGradeState> {
  /// 获取学生成绩列表用例
  final GetStudentGradeListUseCase _getStudentGradeListUseCase;
  
  /// 本地存储
  final LocalStorage _localStorage;
  
  /// 全局计划列表BLoC
  final PlanListGlobalBloc _planListGlobalBloc;

  static const String _tag = 'StudentGradeBloc';

  StudentGradeBloc({
    required GetStudentGradeListUseCase getStudentGradeListUseCase,
    required LocalStorage localStorage,
    required PlanListGlobalBloc planListGlobalBloc,
  })  : _getStudentGradeListUseCase = getStudentGradeListUseCase,
        _localStorage = localStorage,
        _planListGlobalBloc = planListGlobalBloc,
        super(const StudentGradeInitial()) {
    on<LoadStudentGradeEvent>(_onLoadStudentGrade);
    on<RefreshStudentGradeEvent>(_onRefreshStudentGrade);
  }

  /// 处理加载学生成绩事件
  Future<void> _onLoadStudentGrade(
    LoadStudentGradeEvent event,
    Emitter<StudentGradeState> emit,
  ) async {
    Logger.info(_tag, '开始加载学生成绩数据，planId: ${event.planId}');
    
    emit(const StudentGradeLoading());

    final result = await _getStudentGradeListUseCase(
      GetStudentGradeListParams(planId: event.planId),
    );

    result.fold(
      (Failure failure) {
        Logger.error(_tag, '加载学生成绩数据失败: ${failure.message}');
        emit(StudentGradeError(
          message: failure.message,
          planId: event.planId,
        ));
      },
      (grade) {
        Logger.info(_tag, '成功加载学生成绩数据，总分: ${grade.score}');

        // 获取课程名称
        final courseName = _getCourseName();

        // 无论如何都显示数据，即使评分项为空或分数为0
        emit(StudentGradeLoaded(
          grade: grade,
          planId: event.planId,
          courseName: courseName,
        ));
      },
    );
  }

  /// 处理刷新学生成绩事件
  Future<void> _onRefreshStudentGrade(
    RefreshStudentGradeEvent event,
    Emitter<StudentGradeState> emit,
  ) async {
    Logger.info(_tag, '开始刷新学生成绩数据，planId: ${event.planId}');
    
    // 如果当前状态是已加载状态，则显示刷新中状态
    if (state is StudentGradeLoaded) {
      final currentState = state as StudentGradeLoaded;
      emit(StudentGradeRefreshing(
        previousGrade: currentState.grade,
        planId: event.planId,
      ));
    } else {
      emit(const StudentGradeLoading());
    }

    final result = await _getStudentGradeListUseCase(
      GetStudentGradeListParams(planId: event.planId),
    );

    result.fold(
      (Failure failure) {
        Logger.error(_tag, '刷新学生成绩数据失败: ${failure.message}');
        
        // 如果之前有数据，显示刷新失败状态
        if (state is StudentGradeRefreshing) {
          final currentState = state as StudentGradeRefreshing;
          emit(StudentGradeRefreshError(
            message: failure.message,
            previousGrade: currentState.previousGrade,
            planId: event.planId,
          ));
        } else {
          emit(StudentGradeError(
            message: failure.message,
            planId: event.planId,
          ));
        }
      },
      (grade) {
        Logger.info(_tag, '成功刷新学生成绩数据，总分: ${grade.score}');
        
        // 获取课程名称
        final courseName = _getCourseName();
        
        // 检查是否有数据
        if (grade.items.isEmpty && grade.score == 0) {
          emit(StudentGradeEmpty(
            planId: event.planId,
            courseName: courseName,
          ));
        } else {
          emit(StudentGradeLoaded(
            grade: grade,
            planId: event.planId,
            courseName: courseName,
          ));
        }
      },
    );
  }

  /// 获取课程名称
  String _getCourseName() {
    // 从全局计划列表BLoC获取当前选中的计划名称
    final planState = _planListGlobalBloc.state;
    if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
      return planState.currentPlan!.planName;
    }
    
    // 如果无法获取，返回默认名称
    return '2021级市场销售2023-2024实习学年第二学期岗位实习';
  }
}
