/// -----
/// attendance_calendar_state.dart
///
/// 签到日历状态定义
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../../domain/entities/sign_in_record.dart';

/// 签到日历状态基类
abstract class AttendanceCalendarState extends Equatable {
  const AttendanceCalendarState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class AttendanceCalendarInitialState extends AttendanceCalendarState {
  const AttendanceCalendarInitialState();
}

/// 加载中状态
class AttendanceCalendarLoadingState extends AttendanceCalendarState {
  const AttendanceCalendarLoadingState();
}

/// 加载成功状态
class AttendanceCalendarLoadedState extends AttendanceCalendarState {
  /// 签到记录列表
  final List<SignInRecord> records;
  
  /// 当前查询的年份
  final int year;
  
  /// 当前查询的月份
  final int month;
  
  /// 实习计划ID
  final int planId;

  const AttendanceCalendarLoadedState({
    required this.records,
    required this.year,
    required this.month,
    required this.planId,
  });

  /// 获取指定日期的签到记录
  SignInRecord? getRecordByDate(DateTime date) {
    try {
      return records.firstWhere(
        (record) => record.year == date.year && 
                   record.month == date.month && 
                   record.day == date.day,
      );
    } catch (e) {
      return null;
    }
  }
  
  /// 获取正常签到记录数量
  int get normalCount => records.where((record) => record.isNormal).length;
  
  /// 获取异常签到记录数量
  int get abnormalCount => records.where((record) => record.isAbnormal).length;

  /// 复制状态并更新部分属性
  AttendanceCalendarLoadedState copyWith({
    List<SignInRecord>? records,
    int? year,
    int? month,
    int? planId,
  }) {
    return AttendanceCalendarLoadedState(
      records: records ?? this.records,
      year: year ?? this.year,
      month: month ?? this.month,
      planId: planId ?? this.planId,
    );
  }

  @override
  List<Object?> get props => [records, year, month, planId];

  @override
  String toString() {
    return 'AttendanceCalendarLoadedState(records: ${records.length}, year: $year, month: $month, planId: $planId)';
  }
}

/// 加载失败状态
class AttendanceCalendarErrorState extends AttendanceCalendarState {
  /// 错误消息
  final String message;

  const AttendanceCalendarErrorState({
    required this.message,
  });

  @override
  List<Object?> get props => [message];

  @override
  String toString() {
    return 'AttendanceCalendarErrorState(message: $message)';
  }
}

/// 刷新成功状态
class AttendanceCalendarRefreshSuccessState extends AttendanceCalendarLoadedState {
  const AttendanceCalendarRefreshSuccessState({
    required super.records,
    required super.year,
    required super.month,
    required super.planId,
  });

  @override
  String toString() {
    return 'AttendanceCalendarRefreshSuccessState(records: ${records.length}, year: $year, month: $month, planId: $planId)';
  }
}

/// 刷新失败状态
class AttendanceCalendarRefreshErrorState extends AttendanceCalendarState {
  /// 错误消息
  final String message;
  
  /// 之前的签到记录（如果有的话）
  final List<SignInRecord>? previousRecords;
  
  /// 之前的年份
  final int? previousYear;
  
  /// 之前的月份
  final int? previousMonth;
  
  /// 之前的实习计划ID
  final int? previousPlanId;

  const AttendanceCalendarRefreshErrorState({
    required this.message,
    this.previousRecords,
    this.previousYear,
    this.previousMonth,
    this.previousPlanId,
  });

  @override
  List<Object?> get props => [message, previousRecords, previousYear, previousMonth, previousPlanId];

  @override
  String toString() {
    return 'AttendanceCalendarRefreshErrorState(message: $message, previousRecords: ${previousRecords?.length}, previousYear: $previousYear, previousMonth: $previousMonth, previousPlanId: $previousPlanId)';
  }
}
