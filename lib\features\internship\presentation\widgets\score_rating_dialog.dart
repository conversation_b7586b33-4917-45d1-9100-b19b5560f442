/// -----
/// score_rating_dialog.dart
///
/// 评分弹框组件
///
/// <AUTHOR>
/// @date 2025-07-14
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 评分弹框组件
///
/// 用于实习成绩评分，提供0-10分的评分选择
class ScoreRatingDialog extends StatefulWidget {
  /// 评分项标题
  final String title;
  
  /// 评分项权重
  final String weight;
  
  /// 评分项描述
  final String description;
  
  /// 当前分数
  final int? currentScore;
  
  /// 最大分数
  final int maxScore;

  const ScoreRatingDialog({
    Key? key,
    required this.title,
    required this.weight,
    required this.description,
    this.currentScore,
    this.maxScore = 10,
  }) : super(key: key);

  @override
  State<ScoreRatingDialog> createState() => _ScoreRatingDialogState();
}

class _ScoreRatingDialogState extends State<ScoreRatingDialog> {
  int? _selectedScore;

  @override
  void initState() {
    super.initState();
    _selectedScore = widget.currentScore;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: 600.w,
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题区域
            _buildHeader(),
            
            SizedBox(height: 32.h),
            
            // 评分说明
            _buildScoreHint(),
            
            SizedBox(height: 24.h),
            
            // 分数选择区域
            _buildScoreSelection(),
            
            SizedBox(height: 40.h),
            
            // 操作按钮
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建标题区域
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              widget.title,
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.black333,
              ),
            ),
            SizedBox(width: 16.w),
            Container(
              width: 1.w,
              height: 24.h,
              color: AppTheme.grayE5,
            ),
            SizedBox(width: 16.w),
            Text(
              widget.weight,
              style: TextStyle(
                fontSize: 28.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Text(
          widget.description,
          style: TextStyle(
            fontSize: 24.sp,
            color: AppTheme.black666,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  /// 构建评分说明
  Widget _buildScoreHint() {
    return Center(
      child: Text(
        '请为该项目评分（满分${widget.maxScore}分）',
        style: TextStyle(
          fontSize: 28.sp,
          color: AppTheme.black333,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建分数选择区域
  Widget _buildScoreSelection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 6, // 每行6个按钮 (0-5, 6-10分两行)
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.h,
          childAspectRatio: 1.0,
        ),
        itemCount: widget.maxScore + 1, // 0到maxScore
        itemBuilder: (context, index) {
          final score = index;
          final isSelected = _selectedScore == score;
          
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedScore = score;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.primaryColor : Colors.white,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: isSelected ? AppTheme.primaryColor : AppTheme.grayE5,
                  width: 2.w,
                ),
              ),
              child: Center(
                child: Text(
                  score.toString(),
                  style: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? Colors.white : AppTheme.black333,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        // 取消按钮
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.black666,
              side: BorderSide(color: AppTheme.grayE5, width: 2.w),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(vertical: 24.h),
            ),
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 28.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        
        SizedBox(width: 24.w),
        
        // 确定按钮
        Expanded(
          child: ElevatedButton(
            onPressed: _selectedScore != null 
                ? () => Navigator.of(context).pop(_selectedScore)
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _selectedScore != null 
                  ? AppTheme.primaryColor 
                  : AppTheme.black999,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(vertical: 24.h),
              elevation: 0,
            ),
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 28.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 显示评分弹框
Future<int?> showScoreRatingDialog({
  required BuildContext context,
  required String title,
  required String weight,
  required String description,
  int? currentScore,
  int maxScore = 10,
}) async {
  return await showDialog<int?>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return ScoreRatingDialog(
        title: title,
        weight: weight,
        description: description,
        currentScore: currentScore,
        maxScore: maxScore,
      );
    },
  );
}
