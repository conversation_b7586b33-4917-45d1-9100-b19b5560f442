/// -----
/// insurance_remote_data_source_impl.dart
/// 
/// 实习保险远程数据源实现
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:async';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/core/services/auth_expiry_service.dart';
import '../../models/insurance_info_model.dart';
import 'insurance_remote_data_source.dart';

/// 实习保险远程数据源实现
class InsuranceRemoteDataSourceImpl implements InsuranceRemoteDataSource {
  final DioClient _dioClient;
  static const String _tag = 'InsuranceRemoteDataSource';

  const InsuranceRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<InsuranceInfoModel>> getMyInsurance({
    required String planId,
  }) async {
    try {
      Logger.info(_tag, '开始获取实习保险信息，planId: $planId');

      final response = await _dioClient.get(
        'internshipservice/v1/internship/insurance/myInsurance',
        queryParameters: {
          'planId': planId,
        },
      );

      Logger.debug(_tag, '获取实习保险信息响应: $response');

      if (response != null) {
        Logger.debug(_tag, '响应数据类型: ${response.runtimeType}');
        Logger.debug(_tag, '响应数据内容: $response');

        // 处理返回的数据列表
        if (response is List) {
          final insuranceList = <InsuranceInfoModel>[];

          for (int i = 0; i < response.length; i++) {
            try {
              final item = response[i];
              Logger.debug(_tag, '处理第${i + 1}条保险信息: $item');

              if (item is Map<String, dynamic>) {
                final insuranceModel = InsuranceInfoModel.fromJson(item);
                insuranceList.add(insuranceModel);
              } else {
                Logger.warning(_tag, '第${i + 1}条保险信息格式不正确: ${item.runtimeType}');
              }
            } catch (e) {
              Logger.error(_tag, '解析第${i + 1}条保险信息失败: $e');
            }
          }

          Logger.info(_tag, '成功获取${insuranceList.length}条保险信息');
          return insuranceList;
        } else {
          Logger.warning(_tag, '保险信息API响应格式不正确: ${response.runtimeType}');
          Logger.warning(_tag, '响应内容: $response');
          return [];
        }
      } else {
        throw ServerException('响应数据为空');
      }
    } catch (e) {
      Logger.error(_tag, '获取实习保险信息失败: $e');

      // 检查是否是认证失效错误
      if (e is ServerException && e.message.contains('登陆信息失效')) {
        Logger.warning(_tag, '检测到认证失效，触发认证失效处理');

        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 异步处理认证失效，不阻塞当前流程
        unawaited(authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        ).catchError((error) {
          Logger.error(_tag, '处理认证失效时发生错误: $error');
        }));
      }

      if (e is ServerException || e is NetworkException) {
        rethrow;
      } else {
        throw ServerException('获取实习保险信息失败: $e');
      }
    }
  }
}
