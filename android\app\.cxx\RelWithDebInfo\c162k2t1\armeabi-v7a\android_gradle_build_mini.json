{"buildFiles": ["D:\\Dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\flutter_demo\\android\\app\\.cxx\\RelWithDebInfo\\c162k2t1\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\flutter_demo\\android\\app\\.cxx\\RelWithDebInfo\\c162k2t1\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}