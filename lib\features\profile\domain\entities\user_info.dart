/// -----
/// user_info.dart
/// 
/// 用户信息实体，定义用户个人信息的数据结构
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 用户信息实体
///
/// 表示从用户中心接口获取的用户详细信息
/// 包含用户基本信息、学校信息、登录信息等
class UserInfo extends Equatable {
  /// 用户 ID
  final String userId;
  
  /// 用户名称
  final String userName;
  
  /// 用户编号/学号/工号
  final String userCode;
  
  /// 用户类型（0 系统用户 1 学生 2 老师 3 企业 HR）
  final int userType;
  
  /// 头像 URL
  final String? avatar;
  
  /// 手机号码
  final String phone;
  
  /// 性别（0 女 1 男 2 未知）
  final int gender;
  
  /// 最后登录 IP
  final String? loginIp;
  
  /// 最后登录时间（时间戳）
  final int? loginTime;
  
  /// 学校 ID
  final String schoolId;
  
  /// 学校名称
  final String schoolName;
  
  /// 院系 ID
  final String facultyId;
  
  /// 院系名称
  final String facultyName;

  const UserInfo({
    required this.userId,
    required this.userName,
    required this.userCode,
    required this.userType,
    this.avatar,
    required this.phone,
    required this.gender,
    this.loginIp,
    this.loginTime,
    required this.schoolId,
    required this.schoolName,
    required this.facultyId,
    required this.facultyName,
  });

  @override
  List<Object?> get props => [
    userId,
    userName,
    userCode,
    userType,
    avatar,
    phone,
    gender,
    loginIp,
    loginTime,
    schoolId,
    schoolName,
    facultyId,
    facultyName,
  ];

  /// 获取用户类型文本
  String get userTypeText {
    switch (userType) {
      case 0:
        return '系统用户';
      case 1:
        return '学生';
      case 2:
        return '老师';
      case 3:
        return '企业 HR';
      default:
        return '未知';
    }
  }

  /// 获取性别文本
  String get genderText {
    switch (gender) {
      case 0:
        return '女';
      case 1:
        return '男';
      case 2:
        return '未知';
      default:
        return '未知';
    }
  }

  /// 获取格式化的手机号（脱敏显示）
  String get maskedPhone {
    // if (phone.length >= 11) {
    //   return '${phone.substring(0, 3)}*****${phone.substring(8)}';
    // }
    return phone;
  }

  /// 获取格式化的登录时间
  String? get formattedLoginTime {
    if (loginTime == null) return null;
    final dateTime = DateTime.fromMillisecondsSinceEpoch(loginTime!);
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 创建UserInfo的副本，可选择性地更新某些字段
  UserInfo copyWith({
    String? userId,
    String? userName,
    String? userCode,
    int? userType,
    String? avatar,
    String? phone,
    int? gender,
    String? loginIp,
    int? loginTime,
    String? schoolId,
    String? schoolName,
    String? facultyId,
    String? facultyName,
  }) {
    return UserInfo(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userCode: userCode ?? this.userCode,
      userType: userType ?? this.userType,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      gender: gender ?? this.gender,
      loginIp: loginIp ?? this.loginIp,
      loginTime: loginTime ?? this.loginTime,
      schoolId: schoolId ?? this.schoolId,
      schoolName: schoolName ?? this.schoolName,
      facultyId: facultyId ?? this.facultyId,
      facultyName: facultyName ?? this.facultyName,
    );
  }
}
