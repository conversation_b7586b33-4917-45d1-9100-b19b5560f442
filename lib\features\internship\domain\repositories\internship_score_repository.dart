/// -----
/// internship_score_repository.dart
/// 
/// 实习成绩仓库接口，定义实习成绩相关的数据操作
///
/// <AUTHOR>
/// @date 2025-07-16
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_score.dart';
import 'package:flutter_demo/features/internship/domain/entities/score_item.dart';

/// 实习成绩仓库接口
///
/// 定义实习成绩相关的数据操作，遵循Clean Architecture原则
abstract class InternshipScoreRepository {
  /// 获取学生评分列表
  ///
  /// [planId] 计划ID
  /// [type] 评分类型，0:待评分，1:已评分
  ///
  /// 返回 [Either<Failure, List<InternshipScore>>]
  Future<Either<Failure, List<InternshipScore>>> getStudentScoreList({
    required String planId,
    required int type,
  });

  /// 获取评分项列表
  ///
  /// [planId] 计划ID
  ///
  /// 返回 [Either<Failure, List<ScoreItem>>]
  Future<Either<Failure, List<ScoreItem>>> getScoreItems({
    required String planId,
  });

  /// 获取评分详情（已评分）
  ///
  /// [recordId] 评分记录ID
  ///
  /// 返回 [Either<Failure, List<ScoreItem>>]
  Future<Either<Failure, List<ScoreItem>>> getScoreDetail({
    required String recordId,
  });

  /// 提交评分
  ///
  /// [planId] 计划ID
  /// [studentId] 学生ID
  /// [studentName] 学生姓名
  /// [scoreItems] 评分项列表
  ///
  /// 返回 [Either<Failure, void>]
  Future<Either<Failure, void>> submitScore({
    required String planId,
    required String studentId,
    required String studentName,
    required List<ScoreItem> scoreItems,
  });
}
