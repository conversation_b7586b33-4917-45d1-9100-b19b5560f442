/// -----
/// student_grade_remote_data_source_impl.dart
///
/// 学生成绩远程数据源实现，实现获取学生成绩数据的方法
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/grade/data/datasources/student_grade_remote_data_source.dart';
import 'package:flutter_demo/features/grade/data/models/student_grade_model.dart';

/// 学生成绩远程数据源实现
///
/// 实现从远程API获取学生成绩数据的具体逻辑
class StudentGradeRemoteDataSourceImpl implements StudentGradeRemoteDataSource {
  final DioClient _dioClient;

  static const String _tag = 'StudentGradeRemoteDataSource';

  StudentGradeRemoteDataSourceImpl(this._dioClient);

  @override
  Future<StudentGradeDataModel> getStudentGradeList(String planId) async {
    try {
      Logger.info(_tag, '开始获取学生成绩列表，planId: $planId');

      final response = await _dioClient.get(
        'internshipservice/v1/internship/evaluate/student/list',
        queryParameters: {
          'planId': planId,
        },
      );

      Logger.info(_tag, '成功获取学生成绩列表响应');

      // 解析响应数据 - response已经是data部分了
      final responseModel = StudentGradeResponseModel.fromJson({
        'resultCode': '0',
        'resultMsg': 'success',
        'data': response,
      });

      Logger.info(_tag, '成功解析学生成绩数据，总分: ${responseModel.data.score}, 评分项数量: ${responseModel.data.items.length}');

      return responseModel.data;
    } catch (e) {
      Logger.error(_tag, '获取学生成绩列表失败: $e');
      rethrow;
    }
  }
}
