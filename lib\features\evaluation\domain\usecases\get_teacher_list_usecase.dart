/// -----
/// get_teacher_list_usecase.dart
/// 
/// 获取老师列表用例
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/features/evaluation/domain/entities/teacher_evaluation.dart';
import 'package:flutter_demo/features/evaluation/domain/repositories/teacher_evaluation_repository.dart';

/// 获取老师列表用例
/// 
/// 封装获取可评价老师列表的业务逻辑
class GetTeacherListUseCase implements UseCase<List<TeacherEvaluation>, GetTeacherListParams> {
  final TeacherEvaluationRepository _repository;

  GetTeacherListUseCase(this._repository);

  @override
  Future<Either<Failure, List<TeacherEvaluation>>> call(GetTeacherListParams params) async {
    return await _repository.getTeacherList(planId: params.planId);
  }
}

/// 获取老师列表用例参数
/// 
/// 包含获取老师列表所需的实习计划ID
class GetTeacherListParams extends Equatable {
  /// 实习计划ID
  final String planId;

  const GetTeacherListParams({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}
