/// -----
/// internship_score_injection.dart
/// 
/// 实习成绩模块依赖注入配置
///
/// <AUTHOR>
/// @date 2025-07-16
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/features/internship/data/datasources/remote/internship_score_remote_data_source.dart';
import 'package:flutter_demo/features/internship/data/repositories/internship_score_repository_impl.dart';
import 'package:flutter_demo/features/internship/domain/repositories/internship_score_repository.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_bloc.dart';

/// 初始化实习成绩模块依赖
///
/// 注册实习成绩模块的数据源、仓库和BLoC
Future<void> setupInternshipScoreDependencies() async {
  final getIt = GetIt.instance;

  // 数据源
  getIt.registerLazySingleton<InternshipScoreRemoteDataSource>(
    () => InternshipScoreRemoteDataSourceImpl(getIt<DioClient>()),
  );

  // 仓库
  getIt.registerLazySingleton<InternshipScoreRepository>(
    () => InternshipScoreRepositoryImpl(
      remoteDataSource: getIt<InternshipScoreRemoteDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
    ),
  );

  // BLoC - 使用工厂模式，每次都创建新实例
  getIt.registerFactory<InternshipScoreBloc>(
    () => InternshipScoreBloc(
      repository: getIt<InternshipScoreRepository>(),
    ),
  );
}
