/// -----
/// update_user_gender.dart
/// 
/// 更新用户性别用例，封装更新用户性别的业务逻辑
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/profile/domain/repositories/user_info_repository.dart';

/// 更新用户性别用例
///
/// 封装更新用户性别的业务逻辑
/// 实现UseCase接口，遵循Clean Architecture规范
class UpdateUserGender implements UseCase<bool, UpdateUserGenderParams> {
  final UserInfoRepository _repository;
  
  static const String _tag = 'UpdateUserGender';

  UpdateUserGender(this._repository);

  @override
  Future<Either<Failure, bool>> call(UpdateUserGenderParams params) async {
    Logger.info(_tag, '执行更新用户性别用例: ${params.gender}');
    
    try {
      // 验证性别参数
      if (params.gender < 0 || params.gender > 2) {
        Logger.error(_tag, '无效的性别参数: ${params.gender}');
        return Left(ServerFailure('无效的性别参数'));
      }

      // 调用仓库更新用户性别
      final result = await _repository.updateUserGender(params.gender);
      
      return result.fold(
        (failure) {
          Logger.error(_tag, '更新用户性别失败: ${failure.message}');
          return Left(failure);
        },
        (success) {
          Logger.info(_tag, '更新用户性别成功');
          return Right(success);
        },
      );
    } catch (e) {
      Logger.error(_tag, '更新用户性别异常: $e');
      return Left(ServerFailure('更新用户性别异常: $e'));
    }
  }
}

/// 更新用户性别用例参数
///
/// 封装更新用户性别所需的参数
class UpdateUserGenderParams extends Equatable {
  /// 性别（0女 1男 2未知）
  final int gender;

  const UpdateUserGenderParams({
    required this.gender,
  });

  @override
  List<Object?> get props => [gender];
}
