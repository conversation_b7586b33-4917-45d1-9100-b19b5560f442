---
type: "agent_requested"
description: "Example description"
---
# 架构规则

## 整体架构规则

### 必须使用 Clean Architecture 架构
- **表现层(Presentation)**: UI 组件和页面、BLoC 状态管理、路由管理
- **领域层(Domain)**: 业务实体、用例、仓库接口
- **数据层(Data)**: 仓库实现、数据源、数据模型

### 架构原则
1. **依赖规则**: 外层依赖内层，内层不依赖外层
   - 表现层依赖领域层和数据层
   - 领域层不依赖表现层和数据层
   - 数据层依赖领域层，不依赖表现层

2. **依赖注入**: 使用 GetIt 进行依赖注入，解耦组件
   - 在应用启动时注册所有依赖
   - 按模块组织依赖注册
   - 遵循依赖倒置原则

3. **状态管理**: 使用 BLoC 模式管理状态
   - 将 UI 与业务逻辑分离
   - 通过事件驱动状态变化
   - 保持状态不可变

4. **路由管理**: 使用 go_router 进行声明式路由
   - 集中管理所有路由定义
   - 统一页面转场动画
   - 使用路由抽象层简化导航

5. **DRY原则**: 符合DRY原则（Don't Repeat Yourself）
   - 避免重复代码，提取公共逻辑到工具类或基类
   - 相似功能应封装为可复用的组件或方法
   - 重复的业务逻辑应抽象为用例或服务类
   - 重复的UI组件应提取为可复用的Widget
   - 重复的网络请求逻辑应封装为统一的API客户端

## 文件结构规则

### 必须遵循的目录结构
```
lib/
  ├── core/                # 核心工具和通用组件
  │   ├── common/          # 通用组件
  │   ├── config/          # 配置文件
  │   │   ├── env/         # 环境配置
  │   │   └── injection/   # 依赖注入配置
  │   ├── constants/       # 常量定义
  │   ├── error/           # 错误处理
  │   │   ├── exceptions/  # 异常类
  │   │   └── failures/    # 失败类
  │   ├── network/         # 网络相关
  │   │   ├── api/         # API客户端
  │   │   └── interceptors/# 拦截器
  │   ├── router/          # 路由配置
  │   ├── storage/         # 本地存储
  │   ├── theme/           # 主题配置
  │   ├── utils/           # 工具类
  │   └── widgets/         # 共享UI组件
  │       ├── buttons/     # 按钮组件
  │       ├── dialogs/     # 对话框组件
  │       ├── forms/       # 表单组件
  │       ├── indicators/  # 指示器组件
  │       └── states/      # 状态组件（加载、空数据等）
  └── features/            # 功能模块
      ├── [feature_name]/  # 功能模块名
      │   ├── data/        # 数据层
      │   │   ├── datasources/  # 数据源
      │   │   │   ├── local/    # 本地数据源
      │   │   │   └── remote/   # 远程数据源
      │   │   ├── models/       # 数据模型
      │   │   └── repositories/ # 仓库实现
      │   ├── domain/      # 领域层
      │   │   ├── entities/     # 业务实体
      │   │   ├── repositories/ # 仓库接口
      │   │   └── usecases/     # 用例
      │   ├── presentation/ # 表现层
      │   │   ├── bloc/         # BLoC
      │   │   ├── pages/        # 页面
      │   │   └── widgets/      # UI组件
      │   └── di/          # 依赖注入
```

## 依赖注入规则

### 依赖注册方式
- **单例注册**: 使用 `registerLazySingleton` 注册长期存在的服务（如仓库、数据源）
- **工厂注册**: 使用 `registerFactory` 注册短期存在的对象（如 BLoC、用例）
- **作用域注册**: 使用 `registerScopedSingleton` 注册有作用域的单例（如用户会话）
- **异步初始化**: 使用 `registerSingletonAsync` 注册需要异步初始化的依赖
- **依赖获取**: 使用 `getIt<T>()` 获取依赖，避免直接创建实例

### 依赖注入最佳实践
- 在应用启动时初始化所有依赖
- 按功能模块组织依赖注册
- 遵循依赖倒置原则，依赖抽象而非具体实现
- 使用工厂方法创建复杂对象
- 在测试中使用 `registerMock` 替换真实实现
