/// -----
/// teacher_evaluation_model.dart
/// 
/// 老师评价数据模型
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/evaluation/domain/entities/teacher_evaluation.dart';

/// 老师评价数据模型
/// 
/// 用于API数据的序列化和反序列化
class TeacherEvaluationModel extends TeacherEvaluation {
  const TeacherEvaluationModel({
    super.recordId,
    super.teacherId,
    super.teacherName,
    required super.type,
  });

  /// 从JSON创建模型实例
  factory TeacherEvaluationModel.fromJson(Map<String, dynamic> json) {
    return TeacherEvaluationModel(
      recordId: json['recordId']?.toString(),
      teacherId: json['teacherId']?.toString(),
      teacherName: json['teacherName']?.toString(),
      type: json['type'] as int,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'recordId': recordId,
      'teacherId': teacherId,
      'teacherName': teacherName,
      'type': type,
    };
  }

  /// 转换为实体
  TeacherEvaluation toEntity() {
    return TeacherEvaluation(
      recordId: recordId,
      teacherId: teacherId,
      teacherName: teacherName,
      type: type,
    );
  }

  /// 从实体创建模型
  factory TeacherEvaluationModel.fromEntity(TeacherEvaluation entity) {
    return TeacherEvaluationModel(
      recordId: entity.recordId,
      teacherId: entity.teacherId,
      teacherName: entity.teacherName,
      type: entity.type,
    );
  }
}
