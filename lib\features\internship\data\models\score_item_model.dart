/// -----
/// score_item_model.dart
/// 
/// 评分项数据模型类，用于API数据的序列化和反序列化
///
/// <AUTHOR>
/// @date 2025-07-16
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/internship/domain/entities/score_item.dart';

/// 评分项数据模型类
///
/// 用于API数据的序列化和反序列化，对应接口返回的评分项数据结构
class ScoreItemModel {
  /// 评分项标题
  final String title;
  
  /// 评分项内容描述
  final String content;
  
  /// 评分分数，null表示未评分
  final int? score;

  const ScoreItemModel({
    required this.title,
    required this.content,
    this.score,
  });

  /// 从JSON创建评分项数据模型
  factory ScoreItemModel.fromJson(Map<String, dynamic> json) {
    return ScoreItemModel(
      title: json['title'] as String,
      content: json['content'] as String,
      score: json['score'] as int?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'content': content,
      'score': score,
    };
  }

  /// 转换为实体类
  ScoreItem toEntity() {
    return ScoreItem(
      title: title,
      content: content,
      score: score,
    );
  }

  /// 从实体类创建数据模型
  factory ScoreItemModel.fromEntity(ScoreItem entity) {
    return ScoreItemModel(
      title: entity.title,
      content: entity.content,
      score: entity.score,
    );
  }

  /// 创建副本并更新指定字段
  ScoreItemModel copyWith({
    String? title,
    String? content,
    int? score,
  }) {
    return ScoreItemModel(
      title: title ?? this.title,
      content: content ?? this.content,
      score: score ?? this.score,
    );
  }

  @override
  String toString() {
    return 'ScoreItemModel(title: $title, content: $content, score: $score)';
  }
}
