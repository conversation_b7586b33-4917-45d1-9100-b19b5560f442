/// -----
/// sign_in_list_request.dart
///
/// 签到列表请求实体
/// 领域层的签到列表请求数据结构
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 签到列表请求实体
///
/// 领域层的签到列表请求数据结构
class SignInListRequest extends Equatable {
  /// 查询月份，为空默认查当月
  final int? month;
  
  /// 实习计划ID
  final int planId;
  
  /// 查询年，为空默认查本年
  final int? year;

  const SignInListRequest({
    this.month,
    required this.planId,
    this.year,
  });

  /// 复制并更新部分属性
  SignInListRequest copyWith({
    int? month,
    int? planId,
    int? year,
  }) {
    return SignInListRequest(
      month: month ?? this.month,
      planId: planId ?? this.planId,
      year: year ?? this.year,
    );
  }

  @override
  List<Object?> get props => [
    month,
    planId,
    year,
  ];

  @override
  String toString() {
    return 'SignInListRequest(month: $month, planId: $planId, year: $year)';
  }
}
