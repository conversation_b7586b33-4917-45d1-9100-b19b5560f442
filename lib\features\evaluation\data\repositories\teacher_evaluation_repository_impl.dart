/// -----
/// teacher_evaluation_repository_impl.dart
/// 
/// 老师评价仓库实现
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/evaluation/data/datasources/remote/teacher_evaluation_remote_data_source.dart';
import 'package:flutter_demo/features/evaluation/domain/entities/teacher_evaluation.dart';
import 'package:flutter_demo/features/evaluation/domain/repositories/teacher_evaluation_repository.dart';

/// 老师评价仓库实现
/// 
/// 实现老师评价仓库接口，通过数据源获取数据
class TeacherEvaluationRepositoryImpl implements TeacherEvaluationRepository {
  final TeacherEvaluationRemoteDataSource _remoteDataSource;
  
  static const String _tag = 'TeacherEvaluationRepository';

  TeacherEvaluationRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, List<TeacherEvaluation>>> getTeacherList({
    required String planId,
  }) async {
    try {
      Logger.info(_tag, '开始获取老师列表，planId: $planId');
      
      final teachers = await _remoteDataSource.getTeacherList(planId: planId);
      
      // 转换为实体列表
      final entities = teachers.map((model) => model.toEntity()).toList();
      
      Logger.info(_tag, '成功获取${entities.length}个老师');
      return Right(entities);
    } on ServerException catch (e) {
      Logger.error(_tag, '获取老师列表失败: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error(_tag, '网络错误: ${e.message}');
      return Left(NetworkFailure(e.message));
    } catch (e) {
      Logger.error(_tag, '未知错误: $e');
      return Left(ServerFailure('获取老师列表失败: $e'));
    }
  }
}
