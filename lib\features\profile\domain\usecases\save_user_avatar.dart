/// -----
/// save_user_avatar.dart
/// 
/// 保存用户头像用例，处理保存用户头像的业务逻辑
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/features/profile/domain/repositories/user_info_repository.dart';

/// 保存用户头像用例
///
/// 处理保存用户头像的业务逻辑
/// 遵循Clean Architecture的用例模式
class SaveUserAvatar implements UseCase<bool, SaveUserAvatarParams> {
  final UserInfoRepository repository;

  SaveUserAvatar(this.repository);

  @override
  Future<Either<Failure, bool>> call(SaveUserAvatarParams params) async {
    return await repository.saveUserAvatar(params.avatar);
  }
}

/// 保存用户头像参数
///
/// 封装保存用户头像所需的参数
class SaveUserAvatarParams extends Equatable {
  /// 头像URL
  final String avatar;

  const SaveUserAvatarParams({required this.avatar});

  @override
  List<Object?> get props => [avatar];
}
