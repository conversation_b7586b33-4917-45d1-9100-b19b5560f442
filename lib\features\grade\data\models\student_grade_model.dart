/// -----
/// student_grade_model.dart
///
/// 学生成绩数据模型，用于解析API返回的成绩数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/grade/domain/entities/student_grade.dart';

/// 学生成绩响应模型
///
/// 用于解析API返回的成绩数据
class StudentGradeResponseModel {
  final StudentGradeDataModel data;
  final String resultCode;
  final String resultMsg;

  StudentGradeResponseModel({
    required this.data,
    required this.resultCode,
    required this.resultMsg,
  });

  factory StudentGradeResponseModel.fromJson(Map<String, dynamic> json) {
    return StudentGradeResponseModel(
      data: StudentGradeDataModel.fromJson(json['data'] ?? {}),
      resultCode: json['resultCode'] ?? '',
      resultMsg: json['resultMsg'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.toJson(),
      'resultCode': resultCode,
      'resultMsg': resultMsg,
    };
  }
}

/// 学生成绩数据模型
///
/// 包含总分和评分项列表
class StudentGradeDataModel {
  final List<GradeItemModel> items;
  final int score;

  StudentGradeDataModel({
    required this.items,
    required this.score,
  });

  factory StudentGradeDataModel.fromJson(Map<String, dynamic> json) {
    // 简化解析逻辑，直接处理item数组
    final List<GradeItemModel> parsedItems = [];

    if (json['item'] != null && json['item'] is List) {
      final itemsList = json['item'] as List<dynamic>;
      for (final item in itemsList) {
        if (item is Map<String, dynamic>) {
          try {
            final gradeItem = GradeItemModel.fromJson(item);
            parsedItems.add(gradeItem);
          } catch (e) {
            // 解析失败时跳过该项
          }
        }
      }
    }

    // 处理score字段，支持double和int类型
    final dynamic scoreValue = json['score'];
    final int score;
    if (scoreValue is double) {
      score = scoreValue.toInt();
    } else if (scoreValue is int) {
      score = scoreValue;
    } else {
      score = 0;
    }

    return StudentGradeDataModel(
      items: parsedItems,
      score: score,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'item': items.map((item) => item.toJson()).toList(),
      'score': score,
    };
  }

  /// 转换为领域实体
  StudentGrade toEntity() {
    return StudentGrade(
      items: items.map((item) => item.toEntity()).toList(),
      score: score,
    );
  }
}

/// 成绩项模型
///
/// 表示一个评分项的数据
class GradeItemModel {
  final String recordId;
  final int score;
  final int type;
  final String typeName;

  GradeItemModel({
    required this.recordId,
    required this.score,
    required this.type,
    required this.typeName,
  });

  factory GradeItemModel.fromJson(Map<String, dynamic> json) {
    final recordId = json['recordId']?.toString() ?? '';

    // 处理score字段，支持double和int类型
    final dynamic scoreValue = json['score'];
    final int score;
    if (scoreValue is double) {
      score = scoreValue.toInt();
    } else if (scoreValue is int) {
      score = scoreValue;
    } else {
      score = 0;
    }

    final type = json['type'] ?? 0;
    final typeName = json['typeName']?.toString() ?? '';

    return GradeItemModel(
      recordId: recordId,
      score: score,
      type: type,
      typeName: typeName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'recordId': recordId,
      'score': score,
      'type': type,
      'typeName': typeName,
    };
  }

  /// 转换为领域实体
  GradeItem toEntity() {
    return GradeItem(
      recordId: recordId,
      score: score,
      type: type,
      typeName: typeName,
    );
  }
}
