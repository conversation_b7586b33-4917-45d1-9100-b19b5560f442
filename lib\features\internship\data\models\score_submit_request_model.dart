/// -----
/// score_submit_request_model.dart
/// 
/// 评分提交请求数据模型类，用于API请求的序列化
///
/// <AUTHOR>
/// @date 2025-07-16
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 评分提交请求数据模型类
///
/// 用于评分提交API请求的数据结构
class ScoreSubmitRequestModel {
  /// 评分项列表
  final List<ScoreSubmitItemModel> item;
  
  /// 实习计划ID
  final String planId;
  
  /// 学生ID（学生自我评价时可以不传）
  final String? studentId;
  
  /// 学生姓名（学生自我评价时可以不传）
  final String? studentName;

  const ScoreSubmitRequestModel({
    required this.item,
    required this.planId,
    this.studentId,
    this.studentName,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'item': item.map((e) => e.toJson()).toList(),
      'planId': planId,
      if (studentId != null) 'studentId': studentId,
      if (studentName != null) 'studentName': studentName,
    };
  }

  @override
  String toString() {
    return 'ScoreSubmitRequestModel(item: $item, planId: $planId, studentId: $studentId, studentName: $studentName)';
  }
}

/// 评分项数据模型类
///
/// 表示单个评分项的提交数据
class ScoreSubmitItemModel {
  /// 评分项内容描述
  final String content;
  
  /// 评分分数
  final int score;
  
  /// 评分项标题
  final String title;

  const ScoreSubmitItemModel({
    required this.content,
    required this.score,
    required this.title,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'score': score,
      'title': title,
    };
  }

  @override
  String toString() {
    return 'ScoreSubmitItemModel(content: $content, score: $score, title: $title)';
  }
}
