/// -----
/// user_info_repository.dart
/// 
/// 用户信息仓库接口，定义用户信息数据操作的抽象方法
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/features/profile/domain/entities/user_info.dart';

/// 用户信息仓库接口
///
/// 定义用户信息相关的数据操作抽象方法
/// 遵循Clean Architecture的依赖倒置原则
/// 使用Either类型处理成功和失败的情况
abstract class UserInfoRepository {
  /// 获取用户信息
  ///
  /// 从远程API获取当前用户的详细信息
  /// @return Either<Failure, UserInfo> 失败或用户信息实体
  Future<Either<Failure, UserInfo>> getUserInfo();

  /// 更新用户性别
  ///
  /// 更新用户的性别信息
  /// @param gender 性别（0女 1男）
  /// @return Either<Failure, bool> 失败或更新结果
  Future<Either<Failure, bool>> updateUserGender(int gender);

  /// 保存用户头像
  ///
  /// 保存用户的头像信息
  /// @param avatar 头像URL
  /// @return Either<Failure, bool> 失败或保存结果
  Future<Either<Failure, bool>> saveUserAvatar(String avatar);

  /// 修改用户密码
  ///
  /// 修改用户的密码信息
  /// @param oldPassword 旧密码
  /// @param newPassword 新密码
  /// @return Either<Failure, bool> 失败或修改结果
  Future<Either<Failure, bool>> changePassword(String oldPassword, String newPassword);
}
