/// -----
/// user_info_model.dart
/// 
/// 用户信息数据模型，用于处理API响应数据
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/profile/domain/entities/user_info.dart';

/// 用户信息数据模型
///
/// 继承自 UserInfo 实体，用于处理用户中心API响应数据
/// 提供JSON序列化和反序列化功能
class UserInfoModel extends UserInfo {
  const UserInfoModel({
    required String userId,
    required String userName,
    required String userCode,
    required int userType,
    String? avatar,
    required String phone,
    required int gender,
    String? loginIp,
    int? loginTime,
    required String schoolId,
    required String schoolName,
    required String facultyId,
    required String facultyName,
  }) : super(
          userId: userId,
          userName: userName,
          userCode: userCode,
          userType: userType,
          avatar: avatar,
          phone: phone,
          gender: gender,
          loginIp: loginIp,
          loginTime: loginTime,
          schoolId: schoolId,
          schoolName: schoolName,
          facultyId: facultyId,
          facultyName: facultyName,
        );

  /// 从 JSON 创建 UserInfoModel
  ///
  /// 解析用户中心接口返回的JSON数据
  /// @param json API响应的JSON数据
  /// @return UserInfoModel实例
  factory UserInfoModel.fromJson(Map<String, dynamic> json) {
    return UserInfoModel(
      userId: json['userId']?.toString() ?? '',
      userName: json['userName']?.toString() ?? '',
      userCode: json['userCode']?.toString() ?? '',
      userType: _parseIntValue(json['userType']),
      avatar: json['avatar']?.toString(),
      phone: json['phone']?.toString() ?? '',
      gender: _parseIntValue(json['gender']),
      loginIp: json['loginIp']?.toString(),
      loginTime: _parseIntValue(json['loginTime']),
      schoolId: json['schoolId']?.toString() ?? '',
      schoolName: json['schoolName']?.toString() ?? '',
      facultyId: json['facultyId']?.toString() ?? '',
      facultyName: json['facultyName']?.toString() ?? '',
    );
  }

  /// 转换为 JSON
  ///
  /// 将UserInfoModel实例转换为JSON格式
  /// @return JSON格式的Map
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'userCode': userCode,
      'userType': userType,
      'avatar': avatar,
      'phone': phone,
      'gender': gender,
      'loginIp': loginIp,
      'loginTime': loginTime,
      'schoolId': schoolId,
      'schoolName': schoolName,
      'facultyId': facultyId,
      'facultyName': facultyName,
    };
  }

  /// 创建一个新的 UserInfoModel 实例，并更新指定的字段
  ///
  /// @param userId 用户ID
  /// @param userName 用户名称
  /// @param userCode 用户编号
  /// @param userType 用户类型
  /// @param avatar 头像URL
  /// @param phone 手机号码
  /// @param gender 性别
  /// @param loginIp 登录IP
  /// @param loginTime 登录时间
  /// @param schoolId 学校ID
  /// @param schoolName 学校名称
  /// @param facultyId 院系ID
  /// @param facultyName 院系名称
  /// @return 新的UserInfoModel实例
  UserInfoModel copyWith({
    String? userId,
    String? userName,
    String? userCode,
    int? userType,
    String? avatar,
    String? phone,
    int? gender,
    String? loginIp,
    int? loginTime,
    String? schoolId,
    String? schoolName,
    String? facultyId,
    String? facultyName,
  }) {
    return UserInfoModel(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userCode: userCode ?? this.userCode,
      userType: userType ?? this.userType,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      gender: gender ?? this.gender,
      loginIp: loginIp ?? this.loginIp,
      loginTime: loginTime ?? this.loginTime,
      schoolId: schoolId ?? this.schoolId,
      schoolName: schoolName ?? this.schoolName,
      facultyId: facultyId ?? this.facultyId,
      facultyName: facultyName ?? this.facultyName,
    );
  }

  /// 解析整数值
  ///
  /// 安全地将动态类型转换为整数，处理null和字符串类型
  /// @param value 待解析的值
  /// @return 解析后的整数值，默认为0
  static int _parseIntValue(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }
}
