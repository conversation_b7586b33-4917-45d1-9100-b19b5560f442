---
inclusion: always
---

# 导包规则

## 导包顺序规则
导入包时应按照以下顺序排列：
1. **Dart 标准库**: 如 `dart:core`, `dart:async`, `dart:io` 等
2. **Flutter 框架库**: 如 `package:flutter/material.dart`, `package:flutter/widgets.dart` 等
3. **第三方库**: 按字母顺序排列，如 `package:dio/dio.dart`, `package:get_it/get_it.dart` 等
4. **项目内部库**: 按字母顺序排列，使用绝对路径

## 导包路径规范
- **强制使用绝对路径**: 所有导入都必须使用 `package:flutter_demo/` 开头的绝对路径
- **禁止使用相对路径**: 不允许使用 `../`, `./` 等相对路径导入
- **具体导入**: 避免使用 `*` 导入，应指定具体的类或函数

## 导包分组规则
在导包时，应使用空行将不同类型的导入分组：

```dart
import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/auth/domain/entities/user.dart';
```

## 导包别名规则
当存在命名冲突时，应使用 `as` 关键字为导入的库设置别名：

```dart
import 'package:flutter_demo/features/auth/domain/entities/user.dart' as auth;
import 'package:flutter_demo/features/profile/domain/entities/user.dart' as profile;
```

## 条件导包规则
对于平台特定的导入，应使用条件导入：

```dart
import 'package:flutter_demo/core/utils/platform_utils_stub.dart'
    if (dart.library.io) 'package:flutter_demo/core/utils/platform_utils_mobile.dart'
    if (dart.library.html) 'package:flutter_demo/core/utils/platform_utils_web.dart';
```

## 导包最佳实践
- 定期清理未使用的导入
- 使用IDE的自动导入功能，但要检查导入路径是否正确
- 避免循环依赖，如果出现循环依赖，需要重新设计架构
- 对于大型项目，考虑使用 barrel exports 来简化导入

## 禁止的导包方式
❌ 错误示例：
```dart
// 错误：使用相对路径
import '../../../core/constants/constants.dart';
import '../../domain/entities/user.dart';
import './auth_bloc.dart';

// 错误：顺序混乱
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'dart:async';

// 错误：使用通配符导入
import 'package:flutter_demo/core/constants/*';
```

✅ 正确示例：
```dart
// Dart 标准库
import 'dart:async';
import 'dart:convert';

// Flutter 框架库
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 第三方库（按字母顺序）
import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

// 项目内部库（按字母顺序，使用绝对路径）
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/auth/domain/entities/user.dart';
import 'package:flutter_demo/features/auth/presentation/bloc/auth_bloc.dart';
```