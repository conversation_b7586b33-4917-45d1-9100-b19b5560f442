# 项目上下文信息

- 用户选择方案B：简化实现自我评分页面，直接创建UI页面复用internship_score_detail_screen.dart的设计，AppBar标题改为'自我评分'
- 评分页面组件已从 lib/features/common/presentation/screens/ 移动到 lib/core/screens/，包括 score_evaluation_screen.dart 和 score_evaluation_config_factory.dart，相关引用已更新
- 实习评分详情页面已完成接口集成：创建了ScoreItem实体和ScoreItemModel数据模型，在InternshipScoreRemoteDataSource中添加了getScoreItems方法调用/v1/internship/evaluate/teacher/listScoreItem接口，在BLoC中添加了LoadScoreItemsEvent事件处理，在ScoreEvaluationConfigFactory中添加了createDynamicTeacherEvaluationConfig方法支持动态评分项，页面已集成课程头部组件并使用BLoC状态管理替换静态mock数据
- 实习评分详情页面已完成评分提交接口集成：创建了ScoreSubmitRequestModel和ScoreSubmitItemModel数据模型，在InternshipScoreRemoteDataSource中添加了submitScore方法调用/v1/internship/evaluate/saveScore接口，在BLoC中添加了SubmitScoreEvent事件和ScoreSubmitting、ScoreSubmitSuccess、ScoreSubmitError状态处理，页面的_handleSubmit方法已使用BLoC提交评分数据，包含完整的参数验证、错误处理和成功反馈，提交时显示加载遮罩，成功后自动返回上一页
- 修复了InternshipScoreScreen页面中分数显示问题：将硬编码的"实习考核成绩: 待您评分"改为动态显示，已评分的学生显示具体分数（如"实习考核成绩: 99分"），未评分的学生显示"实习考核成绩: 待您评分"
- 实习评分详情页面双路由逻辑：通过type参数区分待评分(0)和已评分(1)，待评分调用/v1/internship/evaluate/teacher/listScoreItem接口，已评分调用/v1/internship/evaluate/scoreDetail接口(参数recordId)，复用现有ScoreItem数据模型和UI组件
- 用户选择方案C实现评价老师评分功能：在evaluation模块创建teacher_evaluation_score_screen.dart页面，复用ScoreEvaluationScreen组件，调用/v1/internship/evaluate/student/listScoreItem接口，数据流为新API→ScoreItemModel→ScoreItem→ScoreEvaluationConfig→ScoreEvaluationScreen
