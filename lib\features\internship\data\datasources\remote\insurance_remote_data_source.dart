/// -----
/// insurance_remote_data_source.dart
/// 
/// 实习保险远程数据源接口定义
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../models/insurance_info_model.dart';

/// 实习保险远程数据源接口
///
/// 定义从远程服务器获取实习保险数据的方法
abstract class InsuranceRemoteDataSource {
  /// 获取我的实习保险信息
  ///
  /// [planId] 实习计划ID
  /// 返回保险信息列表
  Future<List<InsuranceInfoModel>> getMyInsurance({
    required String planId,
  });
}
