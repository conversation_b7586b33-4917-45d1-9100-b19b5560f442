/// -----
/// change_password.dart
/// 
/// 修改用户密码用例，处理修改用户密码的业务逻辑
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/features/profile/domain/repositories/user_info_repository.dart';

/// 修改用户密码用例
///
/// 处理修改用户密码的业务逻辑
/// 遵循Clean Architecture的用例模式
class ChangePassword implements UseCase<bool, ChangePasswordParams> {
  final UserInfoRepository repository;

  ChangePassword(this.repository);

  @override
  Future<Either<Failure, bool>> call(ChangePasswordParams params) async {
    return await repository.changePassword(params.oldPassword, params.newPassword);
  }
}

/// 修改用户密码参数
///
/// 封装修改用户密码所需的参数
class ChangePasswordParams extends Equatable {
  /// 旧密码
  final String oldPassword;
  
  /// 新密码
  final String newPassword;

  const ChangePasswordParams({
    required this.oldPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [oldPassword, newPassword];
}
