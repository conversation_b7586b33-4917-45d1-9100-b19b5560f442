/// -----
/// get_user_info.dart
/// 
/// 获取用户信息用例，封装获取用户信息的业务逻辑
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/profile/domain/entities/user_info.dart';
import 'package:flutter_demo/features/profile/domain/repositories/user_info_repository.dart';

/// 获取用户信息用例
///
/// 封装获取用户信息的业务逻辑
/// 实现UseCase接口，遵循Clean Architecture规范
class GetUserInfo implements UseCase<UserInfo, NoParams> {
  final UserInfoRepository _repository;
  
  static const String _tag = 'GetUserInfo';

  GetUserInfo(this._repository);

  @override
  Future<Either<Failure, UserInfo>> call(NoParams params) async {
    Logger.info(_tag, '执行获取用户信息用例');
    
    try {
      // 调用仓库获取用户信息
      final result = await _repository.getUserInfo();
      
      return result.fold(
        (failure) {
          Logger.error(_tag, '获取用户信息失败: ${failure.message}');
          return Left(failure);
        },
        (userInfo) {
          Logger.info(_tag, '获取用户信息成功: ${userInfo.userName}');
          return Right(userInfo);
        },
      );
    } catch (e) {
      Logger.error(_tag, '获取用户信息异常: $e');
      return Left(ServerFailure('获取用户信息异常: $e'));
    }
  }
}
