/// -----
/// internship_score_model.dart
/// 
/// 实习成绩数据模型类，用于API数据的序列化和反序列化
///
/// <AUTHOR>
/// @date 2025-07-16
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/internship/domain/entities/internship_score.dart';

/// 实习成绩数据模型类
///
/// 用于API数据的序列化和反序列化，对应接口返回的数据结构
class InternshipScoreModel {
  /// 学生ID
  final String studentId;
  
  /// 学生姓名
  final String studentName;
  
  /// 学生电话
  final String phone;
  
  /// 学生头像URL
  final String? avatar;
  
  /// 成绩分数，如果为null则表示未评分
  final String? score;
  
  /// 评分记录ID
  final String? recordId;
  
  /// 创建实习成绩数据模型
  const InternshipScoreModel({
    required this.studentId,
    required this.studentName,
    required this.phone,
    this.avatar,
    this.score,
    this.recordId,
  });
  
  /// 从JSON创建实习成绩数据模型
  factory InternshipScoreModel.fromJson(Map<String, dynamic> json) {
    return InternshipScoreModel(
      studentId: json['studentId']?.toString() ?? '',
      studentName: json['studentName']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      avatar: json['avatar']?.toString(),
      score: json['score']?.toString(),
      recordId: json['recordId']?.toString(),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'studentId': studentId,
      'studentName': studentName,
      'phone': phone,
      'avatar': avatar,
      'score': score,
      'recordId': recordId,
    };
  }
  
  /// 转换为实体类
  InternshipScore toEntity() {
    return InternshipScore(
      studentId: studentId,
      studentName: studentName,
      phone: phone,
      avatar: avatar,
      score: score,
      recordId: recordId,
    );
  }
  
  /// 从实体类创建数据模型
  factory InternshipScoreModel.fromEntity(InternshipScore entity) {
    return InternshipScoreModel(
      studentId: entity.studentId,
      studentName: entity.studentName,
      phone: entity.phone,
      avatar: entity.avatar,
      score: entity.score,
      recordId: entity.recordId,
    );
  }
}
