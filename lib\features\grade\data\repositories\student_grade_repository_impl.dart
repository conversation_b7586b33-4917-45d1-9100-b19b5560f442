/// -----
/// student_grade_repository_impl.dart
///
/// 学生成绩仓库实现，实现获取学生成绩数据的方法
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions/network_exception.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/error/failures/failures.dart';

import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/grade/data/datasources/student_grade_remote_data_source.dart';
import 'package:flutter_demo/features/grade/domain/entities/student_grade.dart';
import 'package:flutter_demo/features/grade/domain/repositories/student_grade_repository.dart';

/// 学生成绩仓库实现
///
/// 实现获取学生成绩数据的方法
class StudentGradeRepositoryImpl implements StudentGradeRepository {
  final StudentGradeRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  static const String _tag = 'StudentGradeRepository';

  StudentGradeRepositoryImpl({
    required StudentGradeRemoteDataSource remoteDataSource,
    required NetworkInfo networkInfo,
  })  : _remoteDataSource = remoteDataSource,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, StudentGrade>> getStudentGradeList(String planId) async {
    Logger.info(_tag, '获取学生成绩列表，planId: $planId');

    if (await _networkInfo.isConnected) {
      try {
        final gradeModel = await _remoteDataSource.getStudentGradeList(planId);
        final grade = gradeModel.toEntity();
        Logger.info(_tag, '成功获取学生成绩列表，总分: ${grade.score}, 评分项数量: ${grade.items.length}');
        return Right(grade);
      } on ServerException catch (e) {
        Logger.error(_tag, '服务器异常: ${e.message}');
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        Logger.error(_tag, '网络异常: ${e.message}');
        return Left(NetworkFailure(e.message));
      } catch (e) {
        Logger.error(_tag, '未知异常: $e');
        return Left(ServerFailure('获取学生成绩失败，请稍后再试'));
      }
    } else {
      Logger.error(_tag, '网络未连接');
      return Left(NetworkFailure('网络未连接，请检查您的网络设置'));
    }
  }
}
