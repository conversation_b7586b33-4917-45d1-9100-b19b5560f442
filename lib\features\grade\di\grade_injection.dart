/// -----
/// grade_injection.dart
///
/// 成绩模块依赖注入配置，注册成绩相关的所有依赖
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import '../data/datasources/student_grade_remote_data_source.dart';
import '../data/datasources/student_grade_remote_data_source_impl.dart';
import '../data/repositories/student_grade_repository_impl.dart';
import '../domain/repositories/student_grade_repository.dart';
import '../domain/usecases/get_student_grade_list_usecase.dart';
import '../presentation/bloc/student_grade/student_grade_bloc.dart';

/// 成绩模块依赖注入配置
class GradeInjection {
  /// 初始化成绩模块的依赖注入
  static void init() {
    final getIt = GetIt.instance;

    // 数据源
    getIt.registerLazySingleton<StudentGradeRemoteDataSource>(
      () => StudentGradeRemoteDataSourceImpl(getIt<DioClient>()),
    );

    // 仓库
    getIt.registerLazySingleton<StudentGradeRepository>(
      () => StudentGradeRepositoryImpl(
        remoteDataSource: getIt<StudentGradeRemoteDataSource>(),
        networkInfo: getIt<NetworkInfo>(),
      ),
    );

    // 用例
    getIt.registerLazySingleton<GetStudentGradeListUseCase>(
      () => GetStudentGradeListUseCase(getIt<StudentGradeRepository>()),
    );

    // BLoC
    getIt.registerFactory<StudentGradeBloc>(
      () => StudentGradeBloc(
        getStudentGradeListUseCase: getIt<GetStudentGradeListUseCase>(),
        localStorage: getIt<LocalStorage>(),
        planListGlobalBloc: getIt<PlanListGlobalBloc>(),
      ),
    );
  }
}
