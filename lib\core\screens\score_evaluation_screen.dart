/// -----
/// score_evaluation_screen.dart
///
/// 通用评分页面，支持自我评分和教师评分等多种场景
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/score_circle_widget.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/score_item_widget.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/score_rating_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 评分项数据模型
class ScoreItemData {
  final String id;
  final String title;
  final String weight;
  final String description;
  int? score;
  final int maxScore;

  ScoreItemData({
    required this.id,
    required this.title,
    required this.weight,
    required this.description,
    this.score,
    required this.maxScore,
  });
}

/// 评分页面配置
class ScoreEvaluationConfig {
  /// 页面标题
  final String pageTitle;
  
  /// 课程名称
  final String courseName;
  
  /// 圆环标题
  final String circleTitle;
  
  /// 评分项列表
  final List<ScoreItemData> scoreItems;
  
  /// 是否需要全部评分才能提交
  final bool requireAllRated;
  
  /// 提交成功消息
  final String submitSuccessMessage;
  
  /// 提交回调
  final Function(Map<String, int?> scores)? onSubmit;

  /// 课程选择回调
  final VoidCallback? onCourseSelect;

  ScoreEvaluationConfig({
    required this.pageTitle,
    required this.courseName,
    required this.circleTitle,
    required this.scoreItems,
    this.requireAllRated = true,
    this.submitSuccessMessage = '评分提交成功',
    this.onSubmit,
    this.onCourseSelect,
  });
}

/// 通用评分页面
///
/// 支持自我评分、教师评分等多种评分场景
class ScoreEvaluationScreen extends StatefulWidget {
  /// 评分页面配置
  final ScoreEvaluationConfig config;

  const ScoreEvaluationScreen({
    Key? key,
    required this.config,
  }) : super(key: key);

  @override
  State<ScoreEvaluationScreen> createState() => _ScoreEvaluationScreenState();
}

class _ScoreEvaluationScreenState extends State<ScoreEvaluationScreen> {
  int? _totalScore;
  final Map<String, int?> _itemScores = {};
  bool _canSubmit = false;

  @override
  void initState() {
    super.initState();
    _initializeScores();
  }

  /// 初始化评分数据
  void _initializeScores() {
    setState(() {
      // 初始化评分项分数
      for (var item in widget.config.scoreItems) {
        _itemScores[item.id] = item.score;
      }
      
      // 计算总分
      _calculateTotalScore();
      
      // 检查是否可以提交
      _updateSubmitStatus();
    });
  }

  /// 处理评分
  Future<void> _handleRate(String itemId) async {
    // 获取当前评分项信息
    final scoreItem = widget.config.scoreItems.firstWhere((item) => item.id == itemId);

    // 显示评分弹框
    final score = await showScoreRatingDialog(
      context: context,
      title: scoreItem.title,
      weight: scoreItem.weight,
      description: scoreItem.description,
      currentScore: scoreItem.score,
      maxScore: scoreItem.maxScore,
    );

    // 如果用户选择了分数并点击了确定
    if (score != null) {
      setState(() {
        _itemScores[itemId] = score;

        // 更新评分项数据
        scoreItem.score = score;

        // 重新计算总分
        _calculateTotalScore();
        
        // 检查是否可以提交
        _updateSubmitStatus();
      });
    }
  }

  /// 计算总分
  void _calculateTotalScore() {
    int total = 0;
    for (var score in _itemScores.values) {
      if (score != null) {
        total += score;
      }
    }
    _totalScore = total;
  }

  /// 更新提交状态
  void _updateSubmitStatus() {
    if (!widget.config.requireAllRated) {
      setState(() {
        _canSubmit = true;
      });
      return;
    }

    bool allRated = true;
    for (var score in _itemScores.values) {
      if (score == null) {
        allRated = false;
        break;
      }
    }
    setState(() {
      _canSubmit = allRated;
    });
  }

  /// 提交评分
  void _submitScores() {
    if (widget.config.requireAllRated && !_canSubmit) {
      AppSnackBar.show(context, '请完成所有项目的评分');
      return;
    }
    
    // 执行提交回调
    widget.config.onSubmit?.call(_itemScores);
    
    AppSnackBar.showSuccess(context, widget.config.submitSuccessMessage);
    // 返回上一页
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    // 获取状态栏高度
    final double statusBarHeight = MediaQuery.of(context).padding.top;
    
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          widget.config.pageTitle,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      extendBodyBehindAppBar: true, // 让AppBar透明
      body: Stack(
        children: [
          // 顶部背景图片
          Positioned(
            child: Image.asset(
              'assets/images/internship_score_detail_top_bg.png',
              height: 628.h,
              fit: BoxFit.fitHeight,
              errorBuilder: (context, error, stackTrace) {
                // 如果图片加载失败，使用渐变背景
                return Container(
                  height: 628.h,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF4A90E2),
                        Color(0xFF7BB3F0),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          
          // 内容区域
          Column(
            children: [
              // 课程头部 - 使用CourseHeaderSection组件
              Container(
                margin: EdgeInsets.only(
                  top: statusBarHeight + kToolbarHeight, // 状态栏高度 + AppBar高度
                  left: 24.w,
                  right: 24.w,
                ),
                child: CourseHeaderSection(
                  courseName: widget.config.courseName,
                  textColor: Colors.white,
                  fontSize: 30.sp,
                  showBackground: false, // 不显示背景容器
                ),
              ),
              
              // 可滚动内容
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                    left: 27.w,
                    right: 27.w,
                    top: 35.h,
                    bottom: 140.h, // 底部留出按钮高度+边距
                  ),
                  child: Column(
                    children: [
                      // 总分圆环
                      _buildScoreCircle(),
                      SizedBox(height: 39.h),
                      
                      // 评分项标题
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 3.w),
                        child: Row(
                          children: [
                            Text(
                              '评分项',
                              style: TextStyle(
                                fontSize: 30.sp,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.black333,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              '每项评分总分10分',
                              style: TextStyle(
                                fontSize: 22.sp,
                                color: AppTheme.black666,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // 评分项列表
                      ...widget.config.scoreItems.map((item) => ScoreItemWidget(
                        title: item.title,
                        weight: item.weight,
                        description: item.description,
                        score: item.score,
                        maxScore: item.maxScore,
                        onRatePressed: widget.config.onSubmit != null ? () => _handleRate(item.id) : null,
                      )),
                      
                      // 底部占位，防止内容被底部固定按钮遮挡
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
          
          // 固定在底部的提交区域（仅在有提交回调时显示）
          if (widget.config.onSubmit != null)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 16.h),
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: SafeArea(
                  top: false,
                  child: SizedBox(
                    height: 88.h,
                    child: ElevatedButton(
                      onPressed: _canSubmit ? _submitScores : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _canSubmit ? AppTheme.primaryColor : AppTheme.black999,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                      ),
                      child: Text(
                        '提交',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 32.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建评分圆环
  Widget _buildScoreCircle() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 46.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          ScoreCircleWidget(
            score: _totalScore ?? 0,
            title: widget.config.circleTitle,
            size: 263,
            strokeWidth: 20,
          ),
        ],
      ),
    );
  }
}