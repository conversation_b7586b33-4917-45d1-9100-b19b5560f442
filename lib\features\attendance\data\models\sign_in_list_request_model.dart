/// -----
/// sign_in_list_request_model.dart
///
/// 签到列表请求模型
/// 封装获取签到列表API请求所需的所有参数
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 签到列表请求模型
///
/// 封装获取签到列表API请求所需的所有参数
class SignInListRequestModel extends Equatable {
  /// 查询月份，为空默认查当月
  final int? month;
  
  /// 实习计划ID
  final int planId;
  
  /// 查询年，为空默认查本年
  final int? year;

  const SignInListRequestModel({
    this.month,
    required this.planId,
    this.year,
  });

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'month': month,
      'planId': planId,
      'year': year,
    };
  }

  /// 从JSON创建实例
  factory SignInListRequestModel.fromJson(Map<String, dynamic> json) {
    return SignInListRequestModel(
      month: json['month'] as int?,
      planId: json['planId'] as int,
      year: json['year'] as int?,
    );
  }

  /// 复制并更新部分属性
  SignInListRequestModel copyWith({
    int? month,
    int? planId,
    int? year,
  }) {
    return SignInListRequestModel(
      month: month ?? this.month,
      planId: planId ?? this.planId,
      year: year ?? this.year,
    );
  }

  @override
  List<Object?> get props => [
    month,
    planId,
    year,
  ];

  @override
  String toString() {
    return 'SignInListRequestModel(month: $month, planId: $planId, year: $year)';
  }
}
