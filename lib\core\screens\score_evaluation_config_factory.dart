/// -----
/// score_evaluation_config_factory.dart
///
/// 评分页面配置工厂，用于创建不同场景的评分配置
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/screens/score_evaluation_screen.dart';
import 'package:flutter_demo/features/internship/domain/entities/score_item.dart';

/// 评分页面配置工厂
class ScoreEvaluationConfigFactory {
  /// 创建自我评分配置
  static ScoreEvaluationConfig createSelfEvaluationConfig({
    required String courseName,
    Function(Map<String, int?> scores)? onSubmit,
  }) {
    return ScoreEvaluationConfig(
      pageTitle: '自我评分',
      courseName: courseName,
      circleTitle: '自我评分\n总分',
      requireAllRated: true,
      submitSuccessMessage: '自我评分提交成功',
      onSubmit: onSubmit,
      scoreItems: [
        ScoreItemData(
          id: 'workAttitude',
          title: '工作态度',
          weight: '20%',
          description: '工作积极性、责任心、团队合作精神',
          maxScore: 10,
        ),
        ScoreItemData(
          id: 'workQuality',
          title: '工作质量',
          weight: '20%',
          description: '任务完成准确性、专业度、细节处理能力',
          maxScore: 10,
        ),
        ScoreItemData(
          id: 'learningAbility',
          title: '学习能力',
          weight: '20%',
          description: '新知识掌握速度、适应能力、创新思维',
          maxScore: 10,
        ),
        ScoreItemData(
          id: 'communication',
          title: '沟通协调',
          weight: '20%',
          description: '与同事沟通、问题解决、协调配合能力',
          maxScore: 10,
        ),
        ScoreItemData(
          id: 'professional',
          title: '专业技能',
          weight: '20%',
          description: '专业知识运用、技能熟练度、实践能力',
          maxScore: 10,
        ),
      ],
    );
  }

  /// 创建教师评分配置
  static ScoreEvaluationConfig createTeacherEvaluationConfig({
    required String courseName,
    Map<String, int?>? existingScores,
    Function(Map<String, int?> scores)? onSubmit,
  }) {
    final scoreItems = [
      ScoreItemData(
        id: 'workQuality1',
        title: '工作质量',
        weight: '20%',
        description: '任务完成准确性、专业度、细节处理能力',
        maxScore: 10,
        score: existingScores?['workQuality1'],
      ),
      ScoreItemData(
        id: 'workQuality2',
        title: '工作质量',
        weight: '20%',
        description: '任务完成准确性、专业度、细节处理能力',
        maxScore: 10,
        score: existingScores?['workQuality2'],
      ),
      ScoreItemData(
        id: 'workQuality3',
        title: '工作质量',
        weight: '20%',
        description: '任务完成准确性、专业度、细节处理能力',
        maxScore: 10,
        score: existingScores?['workQuality3'],
      ),
      ScoreItemData(
        id: 'workQuality4',
        title: '工作质量',
        weight: '20%',
        description: '任务完成准确性、专业度、细节处理能力',
        maxScore: 10,
        score: existingScores?['workQuality4'],
      ),
      ScoreItemData(
        id: 'workQuality5',
        title: '工作质量',
        weight: '20%',
        description: '任务完成准确性、专业度、细节处理能力',
        maxScore: 10,
        score: existingScores?['workQuality5'],
      ),
    ];

    return ScoreEvaluationConfig(
      pageTitle: '成绩评分',
      courseName: courseName,
      circleTitle: '校内老师\n总评分',
      requireAllRated: true,
      submitSuccessMessage: '评分提交成功',
      onSubmit: onSubmit,
      scoreItems: scoreItems,
    );
  }

  /// 创建企业导师评分配置
  static ScoreEvaluationConfig createMentorEvaluationConfig({
    required String courseName,
    Map<String, int?>? existingScores,
    Function(Map<String, int?> scores)? onSubmit,
  }) {
    final scoreItems = [
      ScoreItemData(
        id: 'practicalAbility',
        title: '实践能力',
        weight: '25%',
        description: '实际工作中的操作能力和问题解决能力',
        maxScore: 10,
        score: existingScores?['practicalAbility'],
      ),
      ScoreItemData(
        id: 'teamwork',
        title: '团队协作',
        weight: '20%',
        description: '与团队成员的协作配合能力',
        maxScore: 10,
        score: existingScores?['teamwork'],
      ),
      ScoreItemData(
        id: 'innovation',
        title: '创新思维',
        weight: '20%',
        description: '工作中的创新意识和改进建议',
        maxScore: 10,
        score: existingScores?['innovation'],
      ),
      ScoreItemData(
        id: 'responsibility',
        title: '责任心',
        weight: '20%',
        description: '工作责任感和主动性表现',
        maxScore: 10,
        score: existingScores?['responsibility'],
      ),
      ScoreItemData(
        id: 'growth',
        title: '成长潜力',
        weight: '15%',
        description: '学习成长速度和发展潜力',
        maxScore: 10,
        score: existingScores?['growth'],
      ),
    ];

    return ScoreEvaluationConfig(
      pageTitle: '企业导师评分',
      courseName: courseName,
      circleTitle: '企业导师\n总评分',
      requireAllRated: true,
      submitSuccessMessage: '企业导师评分提交成功',
      onSubmit: onSubmit,
      scoreItems: scoreItems,
    );
  }

  /// 创建自定义评分配置
  static ScoreEvaluationConfig createCustomConfig({
    required String pageTitle,
    required String courseName,
    required String circleTitle,
    required List<ScoreItemData> scoreItems,
    bool requireAllRated = true,
    String submitSuccessMessage = '评分提交成功',
    Function(Map<String, int?> scores)? onSubmit,
  }) {
    return ScoreEvaluationConfig(
      pageTitle: pageTitle,
      courseName: courseName,
      circleTitle: circleTitle,
      scoreItems: scoreItems,
      requireAllRated: requireAllRated,
      submitSuccessMessage: submitSuccessMessage,
      onSubmit: onSubmit,
    );
  }

  /// 根据动态评分项创建教师评分配置
  static ScoreEvaluationConfig createDynamicTeacherEvaluationConfig({
    required String courseName,
    required List<ScoreItem> scoreItems,
    Function(Map<String, int?> scores)? onSubmit,
    VoidCallback? onCourseSelect,
    bool isReadOnly = false,
    String? circleTitle,
    String? pageTitle,
  }) {
    // 将ScoreItem转换为ScoreItemData
    final scoreItemDataList = scoreItems.asMap().entries.map((entry) {
      final index = entry.key;
      final scoreItem = entry.value;

      return ScoreItemData(
        id: 'score_item_$index', // 使用索引作为ID
        title: scoreItem.title,
        weight: '${(100 / scoreItems.length).toStringAsFixed(0)}%', // 平均分配权重
        description: scoreItem.content,
        maxScore: 10, // 默认最高分为10分
        score: scoreItem.score,
      );
    }).toList();

    return ScoreEvaluationConfig(
      pageTitle: pageTitle ?? (isReadOnly ? '成绩详情' : '成绩评分'),
      courseName: courseName,
      circleTitle: circleTitle ?? '校内老师\n总评分',
      requireAllRated: !isReadOnly,
      onSubmit: isReadOnly ? null : onSubmit,
      onCourseSelect: onCourseSelect,
      scoreItems: scoreItemDataList,
    );
  }
}