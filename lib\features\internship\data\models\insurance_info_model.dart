/// -----
/// insurance_info_model.dart
/// 
/// 实习保险信息数据模型
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/internship/domain/entities/insurance_info.dart';

/// 保险文件信息模型
class InsuranceFileModel {
  /// 文件名
  final String fileName;
  
  /// 文件URL
  final String fileUrl;

  const InsuranceFileModel({
    required this.fileName,
    required this.fileUrl,
  });

  /// 从JSON创建实例
  factory InsuranceFileModel.fromJson(Map<String, dynamic> json) {
    try {
      return InsuranceFileModel(
        fileName: json['fileName']?.toString() ?? '',
        fileUrl: json['fileUrl']?.toString() ?? '',
      );
    } catch (e) {
      throw Exception('解析保险文件JSON失败: $e, JSON: $json');
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'fileUrl': fileUrl,
    };
  }
}

/// 实习保险信息数据模型
class InsuranceInfoModel {
  /// 保险名称
  final String insuranceName;
  
  /// 保险单号
  final String insuranceCode;
  
  /// 保险种类 0: 学生实习责任险 1:校方责任险 2:学生平安险 3:其他
  final int insuranceType;
  
  /// 保险购买方 0:学校 1:学生 2:实习单位 3:其他
  final int purchaserType;
  
  /// 保险开始日期（时间戳）
  final int startTime;
  
  /// 保险结束日期（时间戳）
  final int endTime;
  
  /// 实习开始时间（时间戳）
  final int planStartTime;
  
  /// 实习结束时间（时间戳）
  final int planEndTime;
  
  /// 保险文件列表
  final List<InsuranceFileModel> insuranceFileList;
  
  /// 未覆盖天数
  final int uncoveredDays;

  const InsuranceInfoModel({
    required this.insuranceName,
    required this.insuranceCode,
    required this.insuranceType,
    required this.purchaserType,
    required this.startTime,
    required this.endTime,
    required this.planStartTime,
    required this.planEndTime,
    required this.insuranceFileList,
    required this.uncoveredDays,
  });

  /// 从JSON创建实例
  factory InsuranceInfoModel.fromJson(Map<String, dynamic> json) {
    try {
      return InsuranceInfoModel(
        insuranceName: json['insuranceName']?.toString() ?? '',
        insuranceCode: json['insuranceCode']?.toString() ?? '',
        insuranceType: _parseIntSafely(json['insuranceType']),
        purchaserType: _parseIntSafely(json['purchaserType']),
        startTime: _parseIntSafely(json['startTime']),
        endTime: _parseIntSafely(json['endTime']),
        planStartTime: _parseIntSafely(json['planStartTime']),
        planEndTime: _parseIntSafely(json['planEndTime']),
        insuranceFileList: _parseFileList(json['insuranceFileList']),
        uncoveredDays: _parseIntSafely(json['uncoveredDays']),
      );
    } catch (e) {
      throw Exception('解析保险信息JSON失败: $e, JSON: $json');
    }
  }

  /// 安全解析整数
  static int _parseIntSafely(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      final parsed = int.tryParse(value);
      return parsed ?? 0;
    }
    return 0;
  }

  /// 安全解析文件列表
  static List<InsuranceFileModel> _parseFileList(dynamic value) {
    if (value == null) return [];
    if (value is! List) return [];

    final List<InsuranceFileModel> fileList = [];
    for (final item in value) {
      try {
        if (item is Map<String, dynamic>) {
          fileList.add(InsuranceFileModel.fromJson(item));
        }
      } catch (e) {
        // 忽略单个文件解析错误，继续处理其他文件
        continue;
      }
    }
    return fileList;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'insuranceName': insuranceName,
      'insuranceCode': insuranceCode,
      'insuranceType': insuranceType,
      'purchaserType': purchaserType,
      'startTime': startTime,
      'endTime': endTime,
      'planStartTime': planStartTime,
      'planEndTime': planEndTime,
      'insuranceFileList': insuranceFileList.map((file) => file.toJson()).toList(),
      'uncoveredDays': uncoveredDays,
    };
  }

  /// 转换为实体类
  InsuranceInfo toEntity() {
    return InsuranceInfo(
      name: insuranceName,
      policyNumber: insuranceCode,
      purchaser: _getPurchaserText(purchaserType),
      type: _getInsuranceTypeText(insuranceType),
      insurancePeriod: DateTimeRange(
        start: DateTime.fromMillisecondsSinceEpoch(startTime > 0 ? startTime : 0),
        end: DateTime.fromMillisecondsSinceEpoch(endTime > 0 ? endTime : 0),
      ),
      internshipPeriod: DateTimeRange(
        start: DateTime.fromMillisecondsSinceEpoch(planStartTime > 0 ? planStartTime : 0),
        end: DateTime.fromMillisecondsSinceEpoch(planEndTime > 0 ? planEndTime : 0),
      ),
      uncoveredDays: uncoveredDays,
      contractFileName: insuranceFileList.isNotEmpty ? insuranceFileList.first.fileName : null,
      contractFilePath: insuranceFileList.isNotEmpty ? insuranceFileList.first.fileUrl : null,
    );
  }

  /// 获取购买方文本
  String _getPurchaserText(int type) {
    switch (type) {
      case 0:
        return '学校';
      case 1:
        return '学生';
      case 2:
        return '实习单位';
      case 3:
        return '其他';
      default:
        return '未知';
    }
  }

  /// 获取保险种类文本
  String _getInsuranceTypeText(int type) {
    switch (type) {
      case 0:
        return '学生实习责任险';
      case 1:
        return '校方责任险';
      case 2:
        return '学生平安险';
      case 3:
        return '其他';
      default:
        return '未知';
    }
  }
}
