/// -----
/// insurance_repository.dart
/// 
/// 实习保险仓库接口定义
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures.dart';
import '../entities/insurance_info.dart';

/// 实习保险仓库接口
///
/// 定义实习保险相关的数据操作方法
abstract class InsuranceRepository {
  /// 获取我的实习保险信息
  ///
  /// [planId] 实习计划ID
  /// 返回保险信息列表或失败信息
  Future<Either<Failure, List<InsuranceInfo>>> getMyInsurance({
    required String planId,
  });
}
