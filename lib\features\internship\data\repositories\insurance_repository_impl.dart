/// -----
/// insurance_repository_impl.dart
/// 
/// 实习保险仓库实现
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import '../../domain/entities/insurance_info.dart';
import '../../domain/repositories/insurance_repository.dart';
import '../datasources/remote/insurance_remote_data_source.dart';

/// 实习保险仓库实现
class InsuranceRepositoryImpl implements InsuranceRepository {
  final InsuranceRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  const InsuranceRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<InsuranceInfo>>> getMyInsurance({
    required String planId,
  }) async {
    // 检查网络连接
    if (await networkInfo.isConnected) {
      try {
        // 调用远程数据源获取保险信息
        final insuranceModels = await remoteDataSource.getMyInsurance(planId: planId);
        
        // 转换为实体类
        final insuranceInfoList = insuranceModels
            .map((model) => model.toEntity())
            .toList();
        
        return Right(insuranceInfoList);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } on AuthException catch (e) {
        return Left(UnauthorizedFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取保险信息失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }
}
