/// -----
/// student_grade_repository.dart
///
/// 学生成绩仓库接口，定义获取学生成绩数据的方法
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures/failures.dart';
import 'package:flutter_demo/features/grade/domain/entities/student_grade.dart';

/// 学生成绩仓库接口
///
/// 定义获取学生成绩数据的抽象方法
abstract class StudentGradeRepository {
  /// 获取学生成绩列表
  ///
  /// [planId] 实习计划ID
  /// 
  /// 返回 [StudentGrade] 或 [Failure]
  Future<Either<Failure, StudentGrade>> getStudentGradeList(String planId);
}
