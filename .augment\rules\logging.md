---
type: "agent_requested"
description: "Example description"
---
# 日志记录规则

## 日志级别定义

应用中的日志分为以下几个级别，按严重程度从低到高排序：

1. **VERBOSE**: 最详细的日志信息，通常只在开发阶段使用
   - 用于跟踪代码执行流程
   - 记录临时调试信息
   - 仅在开发环境启用

2. **DEBUG**: 调试信息，帮助开发人员理解应用行为
   - 记录函数调用参数和返回值
   - 记录状态变化
   - 记录条件分支选择
   - 在开发和测试环境启用

3. **INFO**: 一般信息，记录应用正常运行过程中的重要事件
   - 记录用户操作（如登录、注销）
   - 记录页面导航
   - 记录重要业务流程的开始和完成
   - 在所有环境中启用

4. **WARNING**: 警告信息，表示可能的问题，但不影响主要功能
   - 记录非关键异常
   - 记录性能问题
   - 记录即将废弃的API使用
   - 在所有环境中启用

5. **ERROR**: 错误信息，表示应用遇到了严重问题
   - 记录影响功能的异常
   - 记录网络请求失败
   - 记录数据库操作失败
   - 在所有环境中启用

6. **FATAL**: 致命错误，表示应用无法继续运行
   - 记录导致应用崩溃的异常
   - 记录严重的资源不足问题
   - 在所有环境中启用

## 日志格式规范

所有日志应遵循以下格式：

```
[级别] [时间] [标签] [消息] [可选:异常信息] [可选:堆栈跟踪]
```

其中：
- **级别**: 日志级别（VERBOSE, DEBUG, INFO, WARNING, ERROR, FATAL）
- **时间**: 格式为 `yyyy-MM-dd HH:mm:ss.SSS`
- **标签**: 用于标识日志来源，通常是类名
- **消息**: 具体的日志内容
- **异常信息**: 如果有异常，记录异常类型和消息
- **堆栈跟踪**: 对于ERROR和FATAL级别，应包含完整的堆栈跟踪

## 日志使用场景指南

### 网络请求日志
- **请求前**: 记录请求URL、方法、头信息和参数（INFO级别）
- **请求成功**: 记录响应状态码和响应数据（DEBUG级别）
- **请求失败**: 记录错误详情和堆栈跟踪（ERROR级别）

### 状态管理日志
- **状态变化**: 记录状态转换（DEBUG级别）
- **事件处理**: 记录收到的事件和处理结果（DEBUG级别）
- **错误状态**: 记录导致错误状态的原因（ERROR级别）

### 生命周期日志
- **页面创建/销毁**: 记录页面生命周期（DEBUG级别）
- **初始化/释放资源**: 记录资源管理（INFO级别）

### 用户操作日志
- **用户交互**: 记录重要的用户操作（INFO级别）
- **操作结果**: 记录操作的结果（INFO/ERROR级别）

## 日志配置规则

应用应支持根据环境配置日志行为：

1. **开发环境**:
   - 启用所有级别的日志
   - 日志输出到控制台
   - 包含详细的堆栈跟踪

2. **测试环境**:
   - 启用INFO级别及以上的日志
   - 日志输出到控制台和文件
   - 包含简化的堆栈跟踪

3. **生产环境**:
   - 启用WARNING级别及以上的日志
   - 日志输出到文件和远程服务器
   - 包含完整的堆栈跟踪
   - 实现日志文件轮转，避免占用过多存储空间

## 敏感信息处理规则

日志中不应包含以下敏感信息：

1. **用户凭证**: 密码、令牌、密钥等
2. **个人身份信息**: 身份证号、银行卡号等
3. **位置信息**: 精确的地理位置信息

对于需要记录的敏感信息，应进行脱敏处理。

## 日志工具实现规则

应用应使用统一的日志工具类，提供以下功能：

1. **不同级别的日志方法**: verbose(), debug(), info(), warning(), error(), fatal()
2. **支持标签**: 每个日志条目都应有标签标识来源
3. **支持异常和堆栈跟踪**: 错误日志应包含异常信息和堆栈跟踪
4. **日志过滤**: 根据级别和标签过滤日志
5. **日志存储**: 支持将日志保存到文件或发送到远程服务器
6. **日志格式化**: 按照规定格式输出日志
