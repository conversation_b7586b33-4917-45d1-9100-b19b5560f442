/// -----
/// teacher_evaluation_state.dart
/// 
/// 老师评价状态
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

part of 'teacher_evaluation_bloc.dart';

/// 老师评价状态基类
abstract class TeacherEvaluationState extends Equatable {
  const TeacherEvaluationState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class TeacherEvaluationInitial extends TeacherEvaluationState {}

/// 加载中状态
class TeacherEvaluationLoading extends TeacherEvaluationState {}

/// 加载成功状态
class TeacherEvaluationLoaded extends TeacherEvaluationState {
  /// 老师列表
  final List<TeacherEvaluation> teachers;
  
  /// 实习计划ID
  final String planId;

  const TeacherEvaluationLoaded({
    required this.teachers,
    required this.planId,
  });

  @override
  List<Object?> get props => [teachers, planId];
}

/// 加载失败状态
class TeacherEvaluationError extends TeacherEvaluationState {
  /// 错误消息
  final String message;
  
  /// 实习计划ID
  final String planId;

  const TeacherEvaluationError({
    required this.message,
    required this.planId,
  });

  @override
  List<Object?> get props => [message, planId];
}
