/// -----
/// teacher_evaluation_bloc.dart
/// 
/// 老师评价BLoC
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/evaluation/domain/entities/teacher_evaluation.dart';
import 'package:flutter_demo/features/evaluation/domain/usecases/get_teacher_list_usecase.dart';

part 'teacher_evaluation_event.dart';
part 'teacher_evaluation_state.dart';

/// 老师评价BLoC
/// 
/// 处理老师评价相关的业务逻辑和状态管理
class TeacherEvaluationBloc extends Bloc<TeacherEvaluationEvent, TeacherEvaluationState> {
  final GetTeacherListUseCase _getTeacherListUseCase;
  
  static const String _tag = 'TeacherEvaluationBloc';

  TeacherEvaluationBloc({
    required GetTeacherListUseCase getTeacherListUseCase,
  }) : _getTeacherListUseCase = getTeacherListUseCase,
       super(TeacherEvaluationInitial()) {
    on<LoadTeacherListEvent>(_onLoadTeacherList);
  }

  /// 处理加载老师列表事件
  Future<void> _onLoadTeacherList(
    LoadTeacherListEvent event,
    Emitter<TeacherEvaluationState> emit,
  ) async {
    Logger.info(_tag, '开始加载老师列表，planId: ${event.planId}');
    
    emit(TeacherEvaluationLoading());

    final result = await _getTeacherListUseCase(
      GetTeacherListParams(planId: event.planId),
    );

    result.fold(
      (failure) {
        Logger.error(_tag, '加载老师列表失败: ${failure.message}');
        emit(TeacherEvaluationError(
          message: failure.message,
          planId: event.planId,
        ));
      },
      (teachers) {
        Logger.info(_tag, '成功加载${teachers.length}个老师');
        emit(TeacherEvaluationLoaded(
          teachers: teachers,
          planId: event.planId,
        ));
      },
    );
  }
}
