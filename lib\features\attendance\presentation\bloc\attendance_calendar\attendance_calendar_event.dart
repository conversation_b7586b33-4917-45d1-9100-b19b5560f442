/// -----
/// attendance_calendar_event.dart
///
/// 签到日历事件定义
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 签到日历事件基类
abstract class AttendanceCalendarEvent extends Equatable {
  const AttendanceCalendarEvent();

  @override
  List<Object?> get props => [];
}

/// 加载签到列表事件
class LoadAttendanceCalendarEvent extends AttendanceCalendarEvent {
  /// 查询月份，为空默认查当月
  final int? month;
  
  /// 查询年，为空默认查本年
  final int? year;
  
  /// 实习计划ID，为空时从全局状态获取
  final int? planId;

  const LoadAttendanceCalendarEvent({
    this.month,
    this.year,
    this.planId,
  });

  @override
  List<Object?> get props => [month, year, planId];

  @override
  String toString() {
    return 'LoadAttendanceCalendarEvent(month: $month, year: $year, planId: $planId)';
  }
}

/// 刷新签到列表事件
class RefreshAttendanceCalendarEvent extends AttendanceCalendarEvent {
  /// 查询月份，为空默认查当月
  final int? month;
  
  /// 查询年，为空默认查本年
  final int? year;
  
  /// 实习计划ID，为空时从全局状态获取
  final int? planId;

  const RefreshAttendanceCalendarEvent({
    this.month,
    this.year,
    this.planId,
  });

  @override
  List<Object?> get props => [month, year, planId];

  @override
  String toString() {
    return 'RefreshAttendanceCalendarEvent(month: $month, year: $year, planId: $planId)';
  }
}

/// 切换月份事件
class ChangeMonthEvent extends AttendanceCalendarEvent {
  /// 新的年份
  final int year;
  
  /// 新的月份
  final int month;
  
  /// 实习计划ID，为空时从全局状态获取
  final int? planId;

  const ChangeMonthEvent({
    required this.year,
    required this.month,
    this.planId,
  });

  @override
  List<Object?> get props => [year, month, planId];

  @override
  String toString() {
    return 'ChangeMonthEvent(year: $year, month: $month, planId: $planId)';
  }
}
