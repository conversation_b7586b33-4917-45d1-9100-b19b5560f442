/// -----
/// teacher_evaluation_remote_data_source_impl.dart
/// 
/// 老师评价远程数据源实现
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/evaluation/data/datasources/remote/teacher_evaluation_remote_data_source.dart';
import 'package:flutter_demo/features/evaluation/data/models/student_evaluate_score_request_model.dart';
import 'package:flutter_demo/features/evaluation/data/models/teacher_evaluation_model.dart';
import 'package:flutter_demo/features/internship/data/models/score_item_model.dart';
import 'package:flutter_demo/features/internship/data/models/score_submit_request_model.dart';

/// 老师评价远程数据源实现
/// 
/// 实现从远程API获取老师评价数据的具体逻辑
class TeacherEvaluationRemoteDataSourceImpl implements TeacherEvaluationRemoteDataSource {
  final DioClient _dioClient;
  
  static const String _tag = 'TeacherEvaluationRemoteDataSource';

  TeacherEvaluationRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<TeacherEvaluationModel>> getTeacherList({
    required String planId,
  }) async {
    try {
      Logger.info(_tag, '开始获取可评价老师列表，planId: $planId');
      
      final response = await _dioClient.get(
        'internshipservice/v1/internship/evaluate/student/listTeacher',
        queryParameters: {
          'planId': planId,
        },
      );

      Logger.debug(_tag, '获取老师列表响应: $response');

      if (response != null) {
        // 处理响应数据 - response已经是data部分了
        if (response is List) {
          final teachers = response
              .map((json) => TeacherEvaluationModel.fromJson(json as Map<String, dynamic>))
              .toList();

          Logger.info(_tag, '成功获取${teachers.length}个可评价老师');
          return teachers;
        } else {
          Logger.warning(_tag, '响应数据格式不正确，期望List但得到: ${response.runtimeType}');
          return [];
        }
      } else {
        Logger.warning(_tag, '响应数据为空');
        return [];
      }
    } catch (e) {
      Logger.error(_tag, '获取老师列表失败: $e');
      
      if (e is ServerException) {
        rethrow;
      } else {
        throw ServerException('获取老师列表失败: $e');
      }
    }
  }

  @override
  Future<List<ScoreItemModel>> getStudentEvaluationScoreItems({
    required String planId,
    required int type,
  }) async {
    try {
      Logger.info(_tag, '开始获取学生评价老师的评分项，planId: $planId, type: $type');

      final response = await _dioClient.get(
        'internshipservice/v1/internship/evaluate/student/listScoreItem',
        queryParameters: {
          'planId': planId,
          'type': type,
        },
      );

      Logger.debug(_tag, '获取评分项响应: $response');

      if (response != null) {
        // 处理响应数据 - response已经是data部分了
        if (response is List) {
          final scoreItems = response
              .map((json) => ScoreItemModel.fromJson(json as Map<String, dynamic>))
              .toList();

          Logger.info(_tag, '成功获取${scoreItems.length}个评分项');
          return scoreItems;
        } else {
          Logger.warning(_tag, '响应数据格式不正确，期望List但得到: ${response.runtimeType}');
          return [];
        }
      } else {
        Logger.warning(_tag, '响应数据为空');
        return [];
      }
    } catch (e) {
      Logger.error(_tag, '获取评分项失败: $e');

      if (e is ServerException) {
        rethrow;
      } else {
        throw ServerException('获取评分项失败: $e');
      }
    }
  }

  @override
  Future<void> submitStudentEvaluationScore({
    required ScoreSubmitRequestModel request,
  }) async {
    try {
      Logger.info(_tag, '开始提交学生评价老师的评分，planId: ${request.planId}');

      // 调用API接口 - 假设使用类似的提交接口，具体接口路径需要根据实际API确定
      await _dioClient.post(
        'internshipservice/v1/internship/evaluate/student/saveScore',
        data: request.toJson(),
      );

      Logger.info(_tag, '成功提交学生评价老师的评分');
    } catch (e) {
      Logger.error(_tag, '提交学生评价老师的评分失败: $e');

      if (e is ServerException) {
        rethrow;
      } else {
        throw ServerException('提交评分失败: $e');
      }
    }
  }

  @override
  Future<void> submitStudentEvaluateScore({
    required StudentEvaluateScoreRequestModel request,
  }) async {
    try {
      Logger.info(_tag, '开始提交学生评价老师的评分（新接口），planId: ${request.planId}, type: ${request.type}');

      // 调用正确的API接口
      await _dioClient.post(
        'internshipservice/v1/internship/evaluate/student/evaluateScore',
        data: request.toJson(),
      );

      Logger.info(_tag, '成功提交学生评价老师的评分（新接口）');
    } catch (e) {
      Logger.error(_tag, '提交学生评价老师的评分失败（新接口）: $e');

      if (e is ServerException) {
        rethrow;
      } else {
        throw ServerException('提交评分失败: $e');
      }
    }
  }

  @override
  Future<List<ScoreItemModel>> getScoreDetail({
    required String recordId,
  }) async {
    try {
      Logger.info(_tag, '开始获取评分详情，recordId: $recordId');

      final response = await _dioClient.get(
        'internshipservice/v1/internship/evaluate/scoreDetail',
        queryParameters: {
          'recordId': recordId,
        },
      );

      Logger.debug(_tag, '获取评分详情响应: $response');

      if (response != null) {
        // 处理响应数据 - 根据接口文档，数据在data.item字段中
        if (response is Map<String, dynamic> && response['item'] is List) {
          final items = response['item'] as List;
          final scoreItems = items
              .map((item) => ScoreItemModel.fromJson(item as Map<String, dynamic>))
              .toList();

          Logger.info(_tag, '成功获取${scoreItems.length}个评分详情项');
          return scoreItems;
        } else {
          Logger.warning(_tag, '响应数据格式不正确，期望包含item字段的Map但得到: ${response.runtimeType}');
          return [];
        }
      } else {
        Logger.warning(_tag, '响应数据为空');
        return [];
      }
    } catch (e) {
      Logger.error(_tag, '获取评分详情失败: $e');

      if (e is ServerException) {
        rethrow;
      } else {
        throw ServerException('获取评分详情失败: $e');
      }
    }
  }
}
