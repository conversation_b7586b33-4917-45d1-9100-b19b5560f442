/// -----
/// user_info_remote_data_source.dart
/// 
/// 用户信息远程数据源接口，定义获取用户信息的抽象方法
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/profile/data/models/user_info_model.dart';

/// 用户信息远程数据源接口
///
/// 定义从远程API获取用户信息的抽象方法
/// 遵循Clean Architecture的依赖倒置原则
abstract class UserInfoRemoteDataSource {
  /// 获取用户信息
  ///
  /// 调用用户中心接口获取当前用户的详细信息
  /// @return Future<UserInfoModel> 用户信息模型
  /// @throws ServerException 服务器错误
  /// @throws NetworkException 网络错误
  Future<UserInfoModel> getUserInfo();

  /// 更新用户性别
  ///
  /// 调用用户中心接口更新用户性别信息
  /// @param gender 性别（0女 1男）
  /// @return Future<bool> 更新是否成功
  /// @throws ServerException 服务器错误
  /// @throws NetworkException 网络错误
  Future<bool> updateUserGender(int gender);

  /// 保存用户头像
  ///
  /// 调用用户中心接口保存用户头像信息
  /// @param avatar 头像URL
  /// @return Future<bool> 保存是否成功
  /// @throws ServerException 服务器错误
  /// @throws NetworkException 网络错误
  Future<bool> saveUserAvatar(String avatar);

  /// 修改用户密码
  ///
  /// 调用用户中心接口修改用户密码
  /// @param oldPassword 旧密码
  /// @param newPassword 新密码
  /// @return Future<bool> 修改是否成功
  /// @throws ServerException 服务器错误
  /// @throws NetworkException 网络错误
  Future<bool> changePassword(String oldPassword, String newPassword);
}
