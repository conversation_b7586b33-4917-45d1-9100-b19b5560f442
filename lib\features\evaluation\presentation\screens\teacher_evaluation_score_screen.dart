/// -----
/// teacher_evaluation_score_screen.dart
/// 
/// 评价老师评分详情页面，学生对老师进行评分
///
/// <AUTHOR>
/// @date 2025-07-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/screens/score_evaluation_config_factory.dart';
import 'package:flutter_demo/core/screens/score_evaluation_screen.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/features/evaluation/data/datasources/remote/teacher_evaluation_remote_data_source.dart';
import 'package:flutter_demo/features/evaluation/data/models/student_evaluate_score_request_model.dart';
import 'package:flutter_demo/features/internship/domain/entities/score_item.dart';
import 'package:flutter_demo/features/internship/data/models/score_item_model.dart';
import 'package:flutter_demo/features/internship/data/models/score_submit_request_model.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';

/// 评价老师评分详情页面
///
/// 学生对指定类型的老师进行评分，支持评分、重新评分和查看功能
class TeacherEvaluationScoreScreen extends StatefulWidget {
  /// 老师类型ID
  final String teacherTypeId;

  /// 老师类型名称
  final String teacherTypeName;

  /// 评价类型，对应老师类型
  final int evaluationType;

  /// 评价记录ID，用于判断是否已评价和获取详情
  final String? recordId;

  /// 路由类型，0:待评分，1:已评分（已弃用，改用recordId判断）
  final int type;

  /// 是否为只读模式（用于查看评分详情）
  final bool isReadOnlyMode;

  const TeacherEvaluationScoreScreen({
    Key? key,
    required this.teacherTypeId,
    required this.teacherTypeName,
    required this.evaluationType,
    this.recordId,
    this.type = 0,
    this.isReadOnlyMode = false,
  }) : super(key: key);

  @override
  State<TeacherEvaluationScoreScreen> createState() => _TeacherEvaluationScoreScreenState();
}

class _TeacherEvaluationScoreScreenState extends State<TeacherEvaluationScoreScreen> {
  late TeacherEvaluationRemoteDataSource _dataSource;
  late PlanListGlobalBloc _planListBloc;
  
  List<ScoreItem> _scoreItems = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _dataSource = GetIt.instance<TeacherEvaluationRemoteDataSource>();
    _planListBloc = GetIt.instance<PlanListGlobalBloc>();
    
    _loadScoreItems();
  }

  /// 加载评分项数据
  Future<void> _loadScoreItems() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      List<ScoreItemModel> scoreItemModels;

      // 根据recordId判断是获取评分项还是评分详情
      if (widget.recordId != null && widget.recordId!.isNotEmpty) {
        // 已评分：获取评分详情（用于重新评分）
        scoreItemModels = await _dataSource.getScoreDetail(
          recordId: widget.recordId!,
        );
      } else {
        // 未评分：获取评分项
        // 获取当前实习计划ID
        String? planId;
        final planState = _planListBloc.state;
        if (planState is PlanListGlobalLoadedState && planState.currentPlanId != null) {
          planId = planState.currentPlanId!;
        }

        if (planId == null) {
          throw Exception('未找到当前实习计划');
        }

        // 调用API获取评分项
        scoreItemModels = await _dataSource.getStudentEvaluationScoreItems(
          planId: planId,
          type: widget.evaluationType,
        );
      }

      // 转换为实体
      final scoreItems = scoreItemModels.map((model) => model.toEntity()).toList();

      setState(() {
        _scoreItems = scoreItems;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  /// 处理评分提交
  Future<void> _handleSubmit(Map<String, int?> scores) async {
    // 显示加载提示
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    try {
      // 获取当前实习计划ID
      String? planId;
      final planState = _planListBloc.state;
      if (planState is PlanListGlobalLoadedState && planState.currentPlanId != null) {
        planId = planState.currentPlanId!;
      }

      if (planId == null) {
        throw Exception('未找到当前实习计划');
      }

      // 构造带有用户评分的评分项列表
      final scoreItems = _scoreItems.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final scoreKey = 'score_item_$index';
        final userScore = scores[scoreKey];

        return item.copyWith(score: userScore);
      }).toList();

      // 验证所有评分项都已评分
      final hasUnratedItems = scoreItems.any((item) => item.score == null);
      if (hasUnratedItems) {
        if (mounted) {
          Navigator.of(context).pop(); // 关闭加载对话框
        }
        AppSnackBar.showError(context, '请完成所有评分项的评分');
        return;
      }

      // 构造请求数据
      final requestItems = scoreItems.map((item) => ScoreSubmitItemModel(
        content: item.content,
        score: item.score ?? 0,
        title: item.title,
      )).toList();

      // 使用新的请求模型和接口
      final request = StudentEvaluateScoreRequestModel(
        item: requestItems,
        planId: planId,
        type: widget.evaluationType, // 使用评价类型
      );

      // 调用新的API接口提交评分
      await _dataSource.submitStudentEvaluateScore(request: request);
      
      if (mounted) {
        // 关闭加载对话框
        Navigator.of(context).pop();
        AppSnackBar.showSuccess(context, '评分提交成功');
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        // 关闭加载对话框
        Navigator.of(context).pop();
        AppSnackBar.showError(context, '提交失败: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 18,
                color: AppTheme.black333,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.black666,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _loadScoreItems,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_scoreItems.isEmpty) {
      return const Center(
        child: Text('暂无评分项'),
      );
    }

    // 获取当前实习计划名称
    String courseName = '加载中...'; // 默认值
    final planState = _planListBloc.state;
    if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
      courseName = planState.currentPlan!.displayName;
    }

    // 根据isReadOnlyMode参数决定是否允许编辑
    final isReadOnly = widget.isReadOnlyMode;

    // 创建评分配置
    final config = ScoreEvaluationConfigFactory.createDynamicTeacherEvaluationConfig(
      courseName: courseName, // 使用实习计划名称
      scoreItems: _scoreItems,
      onSubmit: isReadOnly ? null : _handleSubmit, // 只读模式不允许提交
      isReadOnly: isReadOnly,
      circleTitle: '${widget.teacherTypeName}\n总评分',
      pageTitle: widget.teacherTypeName, // 使用老师类型名称作为页面标题
    );

    // 使用通用评分页面
    return ScoreEvaluationScreen(config: config);
  }
}
