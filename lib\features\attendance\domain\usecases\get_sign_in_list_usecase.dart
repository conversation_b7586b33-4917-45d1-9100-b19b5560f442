/// -----
/// get_sign_in_list_usecase.dart
///
/// 获取签到列表用例
/// 封装获取签到列表的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/utils/logger.dart';
import '../entities/sign_in_list_request.dart';
import '../entities/sign_in_record.dart';
import '../repositories/sign_in_repository.dart';

/// 获取签到列表用例
///
/// 封装获取签到列表的业务逻辑，包括参数验证和数据处理
class GetSignInListUseCase implements UseCase<List<SignInRecord>, GetSignInListParams> {
  final SignInRepository _repository;

  static const String _tag = 'GetSignInListUseCase';

  GetSignInListUseCase({
    required SignInRepository repository,
  }) : _repository = repository;

  @override
  Future<Either<Failure, List<SignInRecord>>> call(GetSignInListParams params) async {
    Logger.info(_tag, '开始执行获取签到列表UseCase: ${params.toString()}');

    // 参数验证
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      Logger.warning(_tag, '参数验证失败: $validationResult');
      return Left(ValidationFailure(validationResult));
    }

    // 创建签到列表请求实体
    final request = SignInListRequest(
      month: params.month,
      planId: params.planId,
      year: params.year,
    );

    // 调用仓库获取数据
    final result = await _repository.getSignInList(request);

    return result.fold(
      (failure) {
        Logger.error(_tag, '获取签到列表失败: ${failure.message}');
        return Left(failure);
      },
      (records) {
        Logger.info(_tag, '获取签到列表成功: ${records.length} 条记录');
        return Right(records);
      },
    );
  }

  /// 验证参数
  String? _validateParams(GetSignInListParams params) {
    if (params.planId <= 0) {
      return '实习计划ID必须大于0';
    }

    if (params.month != null && (params.month! < 1 || params.month! > 12)) {
      return '月份必须在1-12之间';
    }

    if (params.year != null && params.year! < 2000) {
      return '年份不能小于2000';
    }

    return null;
  }
}

/// 获取签到列表用例参数
class GetSignInListParams extends Equatable {
  /// 查询月份，为空默认查当月
  final int? month;
  
  /// 实习计划ID
  final int planId;
  
  /// 查询年，为空默认查本年
  final int? year;

  const GetSignInListParams({
    this.month,
    required this.planId,
    this.year,
  });

  @override
  List<Object?> get props => [
    month,
    planId,
    year,
  ];

  @override
  String toString() {
    return 'GetSignInListParams(month: $month, planId: $planId, year: $year)';
  }
}
