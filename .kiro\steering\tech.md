---
inclusion: always
---

# 技术栈规范

## 核心技术

- **Flutter SDK**: 3.0.0+ (跨平台移动应用框架)
- **Dart SDK**: 3.0.0+ (编程语言)
- **目标平台**: Android, iOS, Web
- **架构模式**: Clean Architecture + BLoC状态管理

## 架构规则

### Clean Architecture分层
- **Data层**: API调用、本地存储、数据模型转换
- **Domain层**: 业务逻辑、实体定义、用例实现
- **Presentation层**: UI组件、状态管理、用户交互

### BLoC状态管理模式
```dart
// 必须继承Equatable以支持状态比较
abstract class FeatureEvent extends Equatable {}
abstract class FeatureState extends Equatable {}

// BLoC必须处理所有事件类型
class FeatureBloc extends Bloc<FeatureEvent, FeatureState> {
  FeatureBloc() : super(FeatureInitial()) {
    on<EventType>(_onEventHandler);
  }
}
```

## 依赖选择规则

### 状态管理
- **必须使用**: `flutter_bloc` - 所有状态管理
- **禁止使用**: Provider, Riverpod, setState (除简单UI状态)

### 网络请求
- **必须使用**: `dio` - HTTP客户端
- **配合使用**: `dartz` - Either类型处理错误
- **错误处理**: 统一使用Failure类封装

### 依赖注入
- **必须使用**: `get_it` - 服务定位器
- **注册位置**: `lib/core/di/injection_container.dart`
- **生命周期**: Repository和UseCase使用单例

### UI组件
- **屏幕适配**: `flutter_screenutil` - 响应式设计
- **图片加载**: `cached_network_image` - 网络图片缓存
- **加载状态**: `skeletonizer` - 骨架屏效果
- **文件选择**: `file_picker` + `image_picker`

### 位置服务
- **必须使用**: `flutter_bmflocation` - 百度定位
- **地图集成**: `flutter_baidu_mapapi_*` - 百度地图
- **权限管理**: `permission_handler` - 运行时权限

## 代码模式

### API调用模式
```dart
// Repository实现必须返回Either类型
Future<Either<Failure, T>> methodName() async {
  try {
    final response = await _dio.post('/api/endpoint');
    return Right(ModelClass.fromJson(response.data));
  } catch (e) {
    return Left(ServerFailure('网络请求失败'));
  }
}
```

### UseCase模式
```dart
class GetUserUseCase {
  final UserRepository _repository;
  
  Future<Either<Failure, User>> call(String userId) {
    return _repository.getUser(userId);
  }
}
```

### BLoC事件处理
```dart
Future<void> _onEventHandler(
  EventType event,
  Emitter<State> emit,
) async {
  emit(LoadingState());
  
  final result = await _useCase(event.params);
  result.fold(
    (failure) => emit(ErrorState(failure.message)),
    (data) => emit(SuccessState(data)),
  );
}
```

## 开发工作流

### 必需命令
```bash
# 依赖安装
flutter pub get

# 代码生成 (mockito, json_serializable)
flutter packages pub run build_runner build --delete-conflicting-outputs

# 运行测试
flutter test

# 构建发布版本
flutter build apk --release
```

### 测试要求
- **单元测试**: 所有UseCase和Repository必须有测试
- **Widget测试**: 关键UI组件必须有测试
- **Mock工具**: 使用`mockito`生成Mock类

## 代码质量规则

### 强制规范
- 使用单引号字符串
- 优先使用const构造函数
- 禁止在生产代码中使用print()
- 所有公共方法必须有文档注释
- 异步方法必须正确处理异常

### 导入顺序
```dart
// 1. Flutter框架
import 'package:flutter/material.dart';

// 2. 第三方包
import 'package:flutter_bloc/flutter_bloc.dart';

// 3. 项目内部
import 'package:flutter_demo/core/error/failures.dart';
```

### 错误处理
- 网络错误使用ServerFailure
- 缓存错误使用CacheFailure  
- 验证错误使用ValidationFailure
- 所有错误信息使用中文