/// -----
/// evaluation_injection.dart
/// 
/// 评价模块依赖注入配置
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/features/evaluation/data/datasources/remote/teacher_evaluation_remote_data_source.dart';
import 'package:flutter_demo/features/evaluation/data/datasources/remote/teacher_evaluation_remote_data_source_impl.dart';
import 'package:flutter_demo/features/evaluation/data/repositories/teacher_evaluation_repository_impl.dart';
import 'package:flutter_demo/features/evaluation/domain/repositories/teacher_evaluation_repository.dart';
import 'package:flutter_demo/features/evaluation/domain/usecases/get_teacher_list_usecase.dart';
import 'package:flutter_demo/features/evaluation/presentation/bloc/teacher_evaluation_bloc.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化评价模块依赖
///
/// 注册评价模块的数据源、仓库、用例和BLoC
Future<void> setupEvaluationDependencies() async {
  // 数据源
  getIt.registerLazySingleton<TeacherEvaluationRemoteDataSource>(
    () => TeacherEvaluationRemoteDataSourceImpl(getIt<DioClient>()),
  );

  // 仓库
  getIt.registerLazySingleton<TeacherEvaluationRepository>(
    () => TeacherEvaluationRepositoryImpl(getIt<TeacherEvaluationRemoteDataSource>()),
  );

  // 用例
  getIt.registerFactory<GetTeacherListUseCase>(
    () => GetTeacherListUseCase(getIt<TeacherEvaluationRepository>()),
  );

  // BLoC - 使用工厂模式，每次使用时创建新实例
  getIt.registerFactory<TeacherEvaluationBloc>(
    () => TeacherEvaluationBloc(
      getTeacherListUseCase: getIt<GetTeacherListUseCase>(),
    ),
  );
}
