/// -----
/// user_info_state.dart
/// 
/// 用户信息BLoC状态类，定义用户信息的各种状态
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/profile/domain/entities/user_info.dart';

/// 用户信息状态基类
///
/// 所有用户信息相关状态的基类
/// 继承Equatable以便于比较和状态变化检测
abstract class UserInfoState extends Equatable {
  const UserInfoState();

  @override
  List<Object?> get props => [];
}

/// 用户信息初始状态
///
/// BLoC的初始状态，表示尚未开始任何操作
class UserInfoInitial extends UserInfoState {
  const UserInfoInitial();

  @override
  List<Object?> get props => [];
}

/// 用户信息加载中状态
///
/// 表示正在从API获取用户信息
class UserInfoLoading extends UserInfoState {
  const UserInfoLoading();

  @override
  List<Object?> get props => [];
}

/// 用户信息加载成功状态
///
/// 表示成功获取到用户信息
class UserInfoLoaded extends UserInfoState {
  /// 用户信息实体
  final UserInfo userInfo;

  const UserInfoLoaded(this.userInfo);

  @override
  List<Object?> get props => [userInfo];
}

/// 用户信息加载失败状态
///
/// 表示获取用户信息时发生错误
class UserInfoError extends UserInfoState {
  /// 错误消息
  final String message;

  const UserInfoError(this.message);

  @override
  List<Object?> get props => [message];
}

/// 用户性别更新中状态
///
/// 表示正在更新用户性别
class UserGenderUpdating extends UserInfoState {
  const UserGenderUpdating();

  @override
  List<Object?> get props => [];
}

/// 用户性别更新成功状态
///
/// 表示用户性别更新成功
class UserGenderUpdateSuccess extends UserInfoState {
  const UserGenderUpdateSuccess();

  @override
  List<Object?> get props => [];
}

/// 用户性别更新失败状态
///
/// 表示用户性别更新失败
class UserGenderUpdateError extends UserInfoState {
  /// 错误消息
  final String message;

  const UserGenderUpdateError(this.message);

  @override
  List<Object?> get props => [message];
}

/// 用户头像保存中状态
///
/// 表示正在保存用户头像
class UserAvatarSaving extends UserInfoState {
  const UserAvatarSaving();

  @override
  List<Object?> get props => [];
}

/// 用户头像保存成功状态
///
/// 表示用户头像保存成功
class UserAvatarSaveSuccess extends UserInfoState {
  const UserAvatarSaveSuccess();

  @override
  List<Object?> get props => [];
}

/// 用户头像保存失败状态
///
/// 表示用户头像保存时发生错误
class UserAvatarSaveError extends UserInfoState {
  /// 错误消息
  final String message;

  const UserAvatarSaveError(this.message);

  @override
  List<Object?> get props => [message];
}

/// 用户密码修改中状态
///
/// 表示正在修改用户密码
class UserPasswordChanging extends UserInfoState {
  const UserPasswordChanging();

  @override
  List<Object?> get props => [];
}

/// 用户密码修改成功状态
///
/// 表示用户密码修改成功
class UserPasswordChangeSuccess extends UserInfoState {
  const UserPasswordChangeSuccess();

  @override
  List<Object?> get props => [];
}

/// 用户密码修改失败状态
///
/// 表示用户密码修改时发生错误
class UserPasswordChangeError extends UserInfoState {
  /// 错误消息
  final String message;

  const UserPasswordChangeError(this.message);

  @override
  List<Object?> get props => [message];
}
