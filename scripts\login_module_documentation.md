# 登录模块架构文档

## 目录
- [1. 模块概述](#1-模块概述)
- [2. 架构设计](#2-架构设计)
- [3. BLoC详细分析](#3-bloc详细分析)
- [4. 数据流时序图](#4-数据流时序图)
- [5. 登录流程图](#5-登录流程图)
- [6. 代码结构](#6-代码结构)
- [7. 依赖注入](#7-依赖注入)
- [8. 错误处理](#8-错误处理)

## 1. 模块概述

登录模块是亿硕教育Flutter应用的核心认证模块，负责用户身份验证、会话管理和权限控制。该模块采用Clean Architecture架构，使用BLoC模式进行状态管理。

### 主要功能
- 用户登录认证
- 用户注册
- 密码重置
- 登录状态检查
- 用户会话管理
- 退出登录

### 技术特点
- **架构模式**: Clean Architecture + BLoC
- **状态管理**: flutter_bloc
- **依赖注入**: get_it
- **数据持久化**: shared_preferences
- **网络请求**: dio
- **错误处理**: dartz (Either类型)

## 2. 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│  LoginScreen  │  LoginForm  │  LoginBloc  │  LoginState/Event│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
├─────────────────────────────────────────────────────────────┤
│   LoginUseCase   │   User Entity   │   AuthRepository       │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                             │
├─────────────────────────────────────────────────────────────┤
│ AuthRepositoryImpl │ RemoteDataSource │ LocalDataSource     │
│    UserModel       │    DioClient     │ SharedPreferences   │
└─────────────────────────────────────────────────────────────┘
```

### 分层职责

#### Presentation Layer (表现层)
- **LoginScreen**: 登录页面容器，提供BLoC实例
- **LoginForm**: 登录表单UI组件，处理用户输入
- **LoginBloc**: 状态管理，处理业务逻辑
- **LoginState/Event**: 状态和事件定义

#### Domain Layer (领域层)
- **LoginUseCase**: 登录用例，封装业务规则
- **User**: 用户实体，核心业务对象
- **AuthRepository**: 认证仓库接口，定义数据操作契约

#### Data Layer (数据层)
- **AuthRepositoryImpl**: 仓库实现，协调数据源
- **RemoteDataSource**: 远程数据源，处理API调用
- **LocalDataSource**: 本地数据源，处理缓存

## 3. BLoC详细分析

### 3.1 LoginBloc 核心组件

```dart
class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginUseCase _loginUseCase;
  final LogoutUseCase _logoutUseCase;
  final GetCurrentUserUseCase _getCurrentUserUseCase;
}
```

### 3.2 事件定义 (LoginEvent)

```dart
abstract class LoginEvent extends Equatable {}

// 登录请求事件
class LoginRequestEvent extends LoginEvent {
  final String phone;
  final String password;
}

// 退出登录事件
class LogoutEvent extends LoginEvent {}

// 检查登录状态事件
class CheckLoginStatusEvent extends LoginEvent {}

// 获取当前用户事件
class GetCurrentUserEvent extends LoginEvent {}
```

### 3.3 状态定义 (LoginState)

```dart
abstract class LoginState extends Equatable {}

// 初始状态
class LoginInitialState extends LoginState {}

// 加载状态
class LoginLoadingState extends LoginState {}

// 登录成功状态
class LoginSuccessState extends LoginState {
  final User user;
}

// 登录失败状态
class LoginFailureState extends LoginState {
  final String message;
}

// 已登录状态
class LoggedInState extends LoginState {
  final User user;
}

// 未登录状态
class NotLoggedInState extends LoginState {}

// 退出成功状态
class LogoutSuccessState extends LoginState {}

// 退出失败状态
class LogoutFailureState extends LoginState {
  final String message;
}
```

### 3.4 BLoC事件处理流程

#### 登录事件处理
```dart
Future<void> _onLoginRequest(
  LoginRequestEvent event,
  Emitter<LoginState> emit,
) async {
  // 1. 发出加载状态
  emit(LoginLoadingState());

  // 2. 调用登录用例
  final result = await _loginUseCase(
    LoginParams(
      phone: event.phone,
      password: event.password,
    ),
  );

  // 3. 处理结果
  result.fold(
    (failure) => emit(LoginFailureState(failure.message)),
    (user) => emit(LoginSuccessState(user)),
  );
}
```

#### 退出登录事件处理
```dart
Future<void> _onLogout(
  LogoutEvent event,
  Emitter<LoginState> emit,
) async {
  emit(LoginLoadingState());
  
  final result = await _logoutUseCase();
  
  result.fold(
    (failure) => emit(LogoutFailureState(failure.message)),
    (_) => emit(LogoutSuccessState()),
  );
}
```

#### 登录状态检查事件处理
```dart
Future<void> _onCheckLoginStatus(
  CheckLoginStatusEvent event,
  Emitter<LoginState> emit,
) async {
  emit(LoginLoadingState());
  
  final result = await _getCurrentUserUseCase();
  
  result.fold(
    (failure) => emit(NotLoggedInState()),
    (user) {
      if (user != null) {
        emit(LoggedInState(user));
      } else {
        emit(NotLoggedInState());
      }
    },
  );
}
```

## 4. 数据流时序图

### 4.1 登录流程时序图

```mermaid
sequenceDiagram
    participant UI as LoginForm
    participant Bloc as LoginBloc
    participant UseCase as LoginUseCase
    participant Repo as AuthRepository
    participant Remote as RemoteDataSource
    participant Local as LocalDataSource
    participant API as Backend API

    UI->>Bloc: LoginRequestEvent(phone, password)
    Bloc->>UI: LoginLoadingState()

    Bloc->>UseCase: call(LoginParams)
    UseCase->>Repo: login(phone, password)

    Repo->>Remote: login(phone, password)
    Remote->>API: POST /v1/user/login
    API-->>Remote: UserData + Token
    Remote-->>Repo: UserModel

    Repo->>Local: cacheUser(userModel)
    Repo->>Local: cacheToken(token)
    Repo->>Local: cacheUserType(userType)
    Local-->>Repo: Success

    Repo-->>UseCase: Right(User)
    UseCase-->>Bloc: Right(User)
    Bloc->>UI: LoginSuccessState(user)

    Note over UI: 导航到主页面
```

### 4.2 登录状态检查时序图

```mermaid
sequenceDiagram
    participant App as Application
    participant Bloc as LoginBloc
    participant UseCase as GetCurrentUserUseCase
    participant Repo as AuthRepository
    participant Local as LocalDataSource

    App->>Bloc: CheckLoginStatusEvent()
    Bloc->>App: LoginLoadingState()

    Bloc->>UseCase: call()
    UseCase->>Repo: getCurrentUser()

    Repo->>Local: getCachedUser()
    Repo->>Local: getCachedToken()

    alt Token exists and valid
        Local-->>Repo: UserModel + Token
        Repo-->>UseCase: Right(User)
        UseCase-->>Bloc: Right(User)
        Bloc->>App: LoggedInState(user)
    else Token invalid or not exists
        Local-->>Repo: null
        Repo-->>UseCase: Right(null)
        UseCase-->>Bloc: Right(null)
        Bloc->>App: NotLoggedInState()
    end
```

### 4.3 退出登录时序图

```mermaid
sequenceDiagram
    participant UI as SettingsScreen
    participant Bloc as LoginBloc
    participant UseCase as LogoutUseCase
    participant Repo as AuthRepository
    participant Remote as RemoteDataSource
    participant Local as LocalDataSource
    participant API as Backend API

    UI->>Bloc: LogoutEvent()
    Bloc->>UI: LoginLoadingState()

    Bloc->>UseCase: call()
    UseCase->>Repo: logout()

    par Remote Logout
        Repo->>Remote: logout()
        Remote->>API: POST /v1/user/logout
        API-->>Remote: Success/Failure
    and Local Cache Clear
        Repo->>Local: clearCache()
        Local-->>Repo: Success
    end

    Repo-->>UseCase: Right(true)
    UseCase-->>Bloc: Right(true)
    Bloc->>UI: LogoutSuccessState()

    Note over UI: 导航到登录页面
```

## 5. 登录流程图

### 5.1 用户登录主流程

```mermaid
flowchart TD
    A[用户打开应用] --> B{检查登录状态}
    B -->|已登录| C[进入主页面]
    B -->|未登录| D[显示登录页面]

    D --> E[用户输入手机号密码]
    E --> F{表单验证}
    F -->|验证失败| G[显示错误信息]
    G --> E
    F -->|验证通过| H[发送登录请求]

    H --> I[显示加载状态]
    I --> J{服务器验证}
    J -->|验证失败| K[显示登录失败]
    K --> D
    J -->|验证成功| L[缓存用户信息]
    L --> M[缓存Token]
    M --> N[缓存用户类型]
    N --> O[登录成功]
    O --> C
```

### 5.2 BLoC状态转换流程

```mermaid
stateDiagram-v2
    [*] --> LoginInitialState

    LoginInitialState --> LoginLoadingState : LoginRequestEvent
    LoginInitialState --> LoginLoadingState : CheckLoginStatusEvent
    LoginInitialState --> LoginLoadingState : GetCurrentUserEvent

    LoginLoadingState --> LoginSuccessState : 登录成功
    LoginLoadingState --> LoginFailureState : 登录失败
    LoginLoadingState --> LoggedInState : 已登录用户
    LoginLoadingState --> NotLoggedInState : 未登录用户

    LoginSuccessState --> LoginLoadingState : LogoutEvent
    LoggedInState --> LoginLoadingState : LogoutEvent

    LoginLoadingState --> LogoutSuccessState : 退出成功
    LoginLoadingState --> LogoutFailureState : 退出失败

    LoginFailureState --> LoginLoadingState : LoginRequestEvent
    NotLoggedInState --> LoginLoadingState : LoginRequestEvent
    LogoutSuccessState --> LoginInitialState : 重新初始化
    LogoutFailureState --> LoggedInState : 退出失败回到已登录状态
```

## 6. 代码结构

### 6.1 目录结构
```
lib/features/auth/
├── data/
│   ├── datasources/
│   │   ├── local/
│   │   │   └── auth_local_data_source.dart
│   │   └── remote/
│   │       └── auth_remote_data_source.dart
│   ├── models/
│   │   └── user_model.dart
│   └── repositories/
│       └── auth_repository_impl.dart
├── domain/
│   ├── entities/
│   │   └── user.dart
│   ├── repositories/
│   │   └── auth_repository.dart
│   └── usecases/
│       ├── login_usecase.dart
│       ├── logout_usecase.dart
│       └── get_current_user_usecase.dart
├── presentation/
│   ├── bloc/
│   │   └── login/
│   │       ├── login_bloc.dart
│   │       ├── login_event.dart
│   │       └── login_state.dart
│   ├── pages/
│   │   └── login_screen.dart
│   └── widgets/
│       └── login_form.dart
└── di/
    └── auth_injection.dart
```

### 6.2 核心类关系

```mermaid
classDiagram
    class LoginBloc {
        -LoginUseCase _loginUseCase
        -LogoutUseCase _logoutUseCase
        -GetCurrentUserUseCase _getCurrentUserUseCase
        +_onLoginRequest()
        +_onLogout()
        +_onCheckLoginStatus()
    }

    class LoginUseCase {
        -AuthRepository _repository
        +call(LoginParams) Either~Failure,User~
    }

    class AuthRepository {
        <<interface>>
        +login(phone, password) Either~Failure,User~
        +logout() Either~Failure,bool~
        +getCurrentUser() Either~Failure,User~
    }

    class AuthRepositoryImpl {
        -AuthRemoteDataSource remoteDataSource
        -AuthLocalDataSource localDataSource
        +login(phone, password)
        +logout()
        +getCurrentUser()
    }

    class User {
        +String userKey
        +String? deptName
        +String? userType
        +String? userName
        +String? token
    }

    LoginBloc --> LoginUseCase
    LoginUseCase --> AuthRepository
    AuthRepository <|-- AuthRepositoryImpl
    AuthRepositoryImpl --> User
```

### 6.3 主要组件详解

#### LoginBloc 实现细节
```dart
class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginUseCase _loginUseCase;
  final LogoutUseCase _logoutUseCase;
  final GetCurrentUserUseCase _getCurrentUserUseCase;

  LoginBloc({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
    required GetCurrentUserUseCase getCurrentUserUseCase,
  })  : _loginUseCase = loginUseCase,
        _logoutUseCase = logoutUseCase,
        _getCurrentUserUseCase = getCurrentUserUseCase,
        super(LoginInitialState()) {
    // 注册事件处理器
    on<LoginRequestEvent>(_onLoginRequest);
    on<LogoutEvent>(_onLogout);
    on<CheckLoginStatusEvent>(_onCheckLoginStatus);
    on<GetCurrentUserEvent>(_onGetCurrentUser);
  }
}
```

#### LoginUseCase 实现细节
```dart
class LoginUseCase implements UseCase<User, LoginParams> {
  final AuthRepository _repository;

  LoginUseCase(this._repository);

  @override
  Future<Either<Failure, User>> call(LoginParams params) async {
    // 参数验证
    if (params.phone.isEmpty || params.password.isEmpty) {
      return Left(ValidationFailure('手机号和密码不能为空'));
    }

    // 手机号格式验证
    if (!_isValidPhone(params.phone)) {
      return Left(ValidationFailure('请输入正确的手机号'));
    }

    // 调用仓库层进行登录
    return await _repository.login(params.phone, params.password);
  }

  bool _isValidPhone(String phone) {
    return RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);
  }
}
```

#### AuthRepositoryImpl 实现细节
```dart
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  AuthRepositoryImpl({
    required AuthRemoteDataSource remoteDataSource,
    required AuthLocalDataSource localDataSource,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource;

  @override
  Future<Either<Failure, User>> login(String phone, String password) async {
    try {
      // 1. 调用远程数据源进行登录
      final userModel = await _remoteDataSource.login(phone, password);

      // 2. 缓存用户信息到本地
      await _localDataSource.cacheUser(userModel);
      await _localDataSource.cacheToken(userModel.token ?? '');
      await _localDataSource.cacheUserType(userModel.userType ?? '');

      // 3. 转换为领域实体并返回
      return Right(userModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('登录失败：${e.toString()}'));
    }
  }
}
```

## 7. 依赖注入

### 7.1 依赖注入配置

```dart
// lib/features/auth/di/auth_injection.dart
void initAuthInjection() {
  // 数据源注册
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(dioClient: sl()),
  );

  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sharedPreferences: sl()),
  );

  // 仓库注册
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
    ),
  );

  // 用例注册
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => GetCurrentUserUseCase(sl()));

  // BLoC注册
  sl.registerFactory(
    () => LoginBloc(
      loginUseCase: sl(),
      logoutUseCase: sl(),
      getCurrentUserUseCase: sl(),
    ),
  );
}
```

### 7.2 依赖关系图

```mermaid
graph TD
    A[LoginScreen] --> B[LoginBloc]
    B --> C[LoginUseCase]
    B --> D[LogoutUseCase]
    B --> E[GetCurrentUserUseCase]

    C --> F[AuthRepository]
    D --> F
    E --> F

    F --> G[AuthRemoteDataSource]
    F --> H[AuthLocalDataSource]

    G --> I[DioClient]
    H --> J[SharedPreferences]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#fff3e0
```

## 8. 错误处理

### 8.1 错误类型定义

```dart
// lib/core/error/failures.dart
abstract class Failure extends Equatable {
  final String message;
  const Failure(this.message);
}

class ServerFailure extends Failure {
  const ServerFailure(String message) : super(message);
}

class CacheFailure extends Failure {
  const CacheFailure(String message) : super(message);
}

class NetworkFailure extends Failure {
  const NetworkFailure(String message) : super(message);
}

class ValidationFailure extends Failure {
  const ValidationFailure(String message) : super(message);
}

class UnknownFailure extends Failure {
  const UnknownFailure(String message) : super(message);
}
```

### 8.2 异常处理策略

```dart
// 在BLoC中处理不同类型的错误
Future<void> _onLoginRequest(
  LoginRequestEvent event,
  Emitter<LoginState> emit,
) async {
  emit(LoginLoadingState());

  final result = await _loginUseCase(
    LoginParams(phone: event.phone, password: event.password),
  );

  result.fold(
    (failure) {
      String errorMessage;
      switch (failure.runtimeType) {
        case ServerFailure:
          errorMessage = '服务器错误：${failure.message}';
          break;
        case NetworkFailure:
          errorMessage = '网络连接失败，请检查网络设置';
          break;
        case ValidationFailure:
          errorMessage = failure.message;
          break;
        default:
          errorMessage = '登录失败，请稍后重试';
      }
      emit(LoginFailureState(errorMessage));
    },
    (user) => emit(LoginSuccessState(user)),
  );
}
```

### 8.3 UI层错误显示

```dart
// 在LoginForm中监听状态变化并显示错误
BlocListener<LoginBloc, LoginState>(
  listener: (context, state) {
    if (state is LoginFailureState) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    } else if (state is LoginSuccessState) {
      // 登录成功，导航到主页面
      context.go('/home');
    }
  },
  child: BlocBuilder<LoginBloc, LoginState>(
    builder: (context, state) {
      return Form(
        // 表单内容
      );
    },
  ),
)
```

## 9. 最佳实践

### 9.1 BLoC使用最佳实践

1. **状态不可变性**：所有状态类都应该是不可变的，使用`Equatable`进行比较
2. **事件命名规范**：事件名称应该清晰描述用户意图，如`LoginRequestEvent`
3. **状态粒度控制**：避免过于细粒度的状态，保持状态的语义完整性
4. **错误处理统一**：使用`Either`类型统一处理成功和失败情况
5. **依赖注入**：通过构造函数注入依赖，便于测试和维护

### 9.2 性能优化建议

1. **BLoC实例管理**：在适当的Widget层级提供BLoC，避免不必要的重建
2. **状态监听优化**：使用`BlocListener`处理副作用，`BlocBuilder`处理UI重建
3. **缓存策略**：合理使用本地缓存，减少网络请求
4. **内存管理**：及时释放不需要的资源，避免内存泄漏

### 9.3 测试策略

1. **单元测试**：为每个用例、仓库、数据源编写单元测试
2. **BLoC测试**：使用`bloc_test`包测试BLoC的状态转换
3. **集成测试**：测试完整的登录流程
4. **Mock测试**：使用Mock对象隔离外部依赖

### 9.4 安全考虑

1. **密码加密**：在传输前对密码进行加密处理
2. **Token管理**：安全存储和管理访问令牌
3. **会话超时**：实现会话超时机制
4. **输入验证**：对所有用户输入进行严格验证

## 10. 总结

登录模块采用Clean Architecture + BLoC的架构设计，具有以下优势：

- **职责分离**：各层职责明确，便于维护和扩展
- **可测试性**：依赖注入和接口抽象使得单元测试更容易
- **状态管理**：BLoC模式提供了清晰的状态管理方案
- **错误处理**：统一的错误处理机制提高了用户体验
- **可扩展性**：模块化设计便于添加新功能

该架构为整个应用的其他模块提供了良好的设计参考，确保了代码的一致性和可维护性。
