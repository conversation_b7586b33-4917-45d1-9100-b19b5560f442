/// -----
/// teacher_evaluation.dart
/// 
/// 老师评价实体
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 老师评价实体
/// 
/// 表示一个可评价的老师信息
class TeacherEvaluation extends Equatable {
  /// 实习评价记录ID
  final String? recordId;
  
  /// 老师ID
  final String? teacherId;
  
  /// 老师名称
  final String? teacherName;
  
  /// 评价类型
  /// 1: 评价班主任老师
  /// 2: 学生评价指导老师
  /// 3: 学生评价企业老师
  final int type;

  const TeacherEvaluation({
    this.recordId,
    this.teacherId,
    this.teacherName,
    required this.type,
  });

  /// 获取评价类型对应的显示名称
  String get typeName {
    switch (type) {
      case 1:
        return '评价班主任';
      case 2:
        return '评价校内指导老师';
      case 3:
        return '评价企业指导老师';
      default:
        return '未知类型';
    }
  }

  @override
  List<Object?> get props => [recordId, teacherId, teacherName, type];

  @override
  String toString() {
    return 'TeacherEvaluation(recordId: $recordId, teacherId: $teacherId, teacherName: $teacherName, type: $type)';
  }
}
