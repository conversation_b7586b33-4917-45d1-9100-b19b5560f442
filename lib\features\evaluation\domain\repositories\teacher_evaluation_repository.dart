/// -----
/// teacher_evaluation_repository.dart
/// 
/// 老师评价仓库接口
///
/// <AUTHOR>
/// @date 2025-01-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures.dart';
import 'package:flutter_demo/features/evaluation/domain/entities/teacher_evaluation.dart';

/// 老师评价仓库接口
/// 
/// 定义获取老师评价相关数据的操作
abstract class TeacherEvaluationRepository {
  /// 获取可评价的老师列表
  /// 
  /// [planId] 实习计划ID
  /// 返回 Either<Failure, List<TeacherEvaluation>>，表示获取成功或失败
  Future<Either<Failure, List<TeacherEvaluation>>> getTeacherList({
    required String planId,
  });
}
