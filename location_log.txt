I/flutter (  707): Button onPressed: 签到
I/flutter (  707): [ERROR] [2025-07-10 10:59:43.154] [_handleFunctionItemTap] 点击功能项: 签到, 路由: /student_sign_in, 用户类型: 1
I/flutter (  707): ModuleNavigationService: 导航到模块 签到, 路由名称: , 路由路径: /student_sign_in, 用户类型: 1
I/flutter (  707): Analytics Event: module_click, Parameters: {title: 签到, route: /student_sign_in, routeName: , userType: 1}
I/flutter (  707): 📱 [签到页面] initState 开始...
I/flutter (  707): 📱 [签到页面] 开始初始化定位服务...
I/flutter (  707): 📱 [签到页面] initState 完成
W/1.raster(  707): type=1400 audit(0.0:175878): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
I/flutter (  707): 🆕 [定位服务] 创建新的定位服务实例
I/flutter (  707): 🛑 [定位服务] 停止定位...
W/WindowOnBackDispatcher(  707): OnBackInvokedCallback is not enabled for the application.
W/WindowOnBackDispatcher(  707): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175879): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175880): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
W/1.raster(  707): type=1400 audit(0.0:175881): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175882): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
W/1.raster(  707): type=1400 audit(0.0:175883): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175884): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory

══╡ EXCEPTION CAUGHT BY IMAGE RESOURCE SERVICE ╞════════════════════════════════════════════════════
The following assertion was thrown resolving an image codec:
Unable to load asset: "assets/images/laptop_illustration.png".
Exception: Asset not found

When the exception was thrown, this was the stack:
#0      PlatformAssetBundle.loadBuffer (package:flutter/src/services/asset_bundle.dart:374:7)
<asynchronous suspension>
#1      AssetBundleImageProvider._loadAsync (package:flutter/src/painting/image_provider.dart:787:16)
<asynchronous suspension>
#2      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1048:3)
<asynchronous suspension>

Image provider: AssetImage(bundle: null, name: "assets/images/laptop_illustration.png")
Image key: AssetBundleImageKey(bundle: PlatformAssetBundle#3a26b(), name:
  "assets/images/laptop_illustration.png", scale: 1.0)
════════════════════════════════════════════════════════════════════════════════════════════════════

E/LB      (  707): fail to open node: No such file or directory
I/flutter (  707): 📱 [签到页面] 当前定位状态: LocationStatus.initial
I/flutter (  707): 📱 [签到页面] 开始刷新定位...
D/ProfileInstaller(  707): Installing profile for com.yishuo.internship
I/flutter (  707): 🔄 [定位服务] 刷新定位... 当前初始化状态: false
I/flutter (  707): 🔐 [定位服务] 检查定位权限...
I/flutter (  707): ✅ [定位服务] 定位权限已授权
I/flutter (  707): 🔄 [定位服务] 服务未初始化，重新初始化...
I/flutter (  707): 🗺️ [定位服务] 开始初始化... 当前状态: _isInitialized=false
I/flutter (  707): 🔄 [定位服务] 强制重置服务状态...
I/flutter (  707): ✅ [定位服务] 强制重置完成
I/flutter (  707): 🗺️ [定位服务] 执行完整初始化流程...
I/flutter (  707): 🔐 [定位服务] 检查定位权限...
I/flutter (  707): 📱 [签到页面] 收到定位状态变化: LocationStatus.initial, 位置: 正在获取位置信息...
I/flutter (  707): 📱 [签到页面] 收到定位状态变化: LocationStatus.loading, 位置: 正在获取位置信息...
I/flutter (  707): ✅ [定位服务] 定位权限已授权
I/flutter (  707): 🗺️ [定位服务] 初始化百度地图SDK...
I/flutter (  707): ✅ [定位服务] 百度地图SDK设置完成
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175906): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175907): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175908): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
W/1.raster(  707): type=1400 audit(0.0:175909): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175910): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/shuo.internship(  707): FrameInsert open fail: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
I/flutter (  707): 🔒 [定位服务] 设置隐私合规...
I/flutter (  707): flutter_bmflocation/setAgreePrivacy
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
D/TrafficStats(  707): tagSocket(219) with statsTag=0xffffffff, statsUid=-1
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
I/flutter (  707): ⚙️ [定位服务] 配置定位参数...
I/flutter (  707): ✅ [定位服务] 初始化完成
I/flutter (  707): 🚀 [定位服务] 开始定位... 初始化状态: true, 插件状态: true
I/flutter (  707): 🚀 [定位服务] 执行定位...
I/flutter (  707): 📡 [定位服务] 设置定位回调...
I/flutter (  707): 🚀 [定位服务] 启动Android定位...
I/flutter (  707): 📱 [签到页面] 收到定位状态变化: LocationStatus.loading, 位置: 正在获取位置信息...
W/System.err(  707): javax.crypto.BadPaddingException: error:04000089:RSA routines:OPENSSL_internal:PKCS_DECODING_ERROR
W/System.err(  707):    at com.android.org.conscrypt.NativeCrypto.RSA_private_decrypt(Native Method)
W/System.err(  707):    at com.android.org.conscrypt.OpenSSLCipherRSA$DirectRSA.doCryptoOperation(OpenSSLCipherRSA.java:404)
W/System.err(  707):    at com.android.org.conscrypt.OpenSSLCipherRSA.engineDoFinal(OpenSSLCipherRSA.java:316)
W/System.err(  707):    at javax.crypto.Cipher.doFinal(Cipher.java:2074)
W/System.err(  707):    at com.baidu.lbsapi.auth.r.b(Unknown Source:38)
W/System.err(  707):    at com.baidu.lbsapi.auth.LBSAuthManager.a(Unknown Source:28)
W/System.err(  707):    at com.baidu.lbsapi.auth.LBSAuthManager.a(Unknown Source:39)
W/System.err(  707):    at com.baidu.lbsapi.auth.LBSAuthManager.a(Unknown Source:0)
W/System.err(  707):    at com.baidu.lbsapi.auth.m.run(Unknown Source:43)
W/System.err(  707):    at android.os.Handler.handleCallback(Handler.java:959)
W/System.err(  707):    at android.os.Handler.dispatchMessage(Handler.java:100)
W/System.err(  707):    at android.os.Looper.loopOnce(Looper.java:249)
W/System.err(  707):    at android.os.Looper.loop(Looper.java:337)
W/System.err(  707):    at com.baidu.lbsapi.auth.p.run(Unknown Source:19)
W/System.err(  707): javax.crypto.BadPaddingException: error:04000089:RSA routines:OPENSSL_internal:PKCS_DECODING_ERROR
W/System.err(  707):    at com.android.org.conscrypt.NativeCrypto.RSA_private_decrypt(Native Method)
W/System.err(  707):    at com.android.org.conscrypt.OpenSSLCipherRSA$DirectRSA.doCryptoOperation(OpenSSLCipherRSA.java:404)
W/System.err(  707):    at com.android.org.conscrypt.OpenSSLCipherRSA.engineDoFinal(OpenSSLCipherRSA.java:316)
W/System.err(  707):    at javax.crypto.Cipher.doFinal(Cipher.java:2074)
W/System.err(  707):    at com.baidu.lbsapi.auth.r.b(Unknown Source:38)
W/System.err(  707):    at com.baidu.lbsapi.auth.LBSAuthManager.a(Unknown Source:28)
W/System.err(  707):    at com.baidu.lbsapi.auth.LBSAuthManager.a(Unknown Source:39)
W/System.err(  707):    at com.baidu.lbsapi.auth.LBSAuthManager.a(Unknown Source:0)
W/System.err(  707):    at com.baidu.lbsapi.auth.m.run(Unknown Source:79)
W/System.err(  707):    at android.os.Handler.handleCallback(Handler.java:959)
W/System.err(  707):    at android.os.Handler.dispatchMessage(Handler.java:100)
W/System.err(  707):    at android.os.Looper.loopOnce(Looper.java:249)
W/System.err(  707):    at android.os.Looper.loop(Looper.java:337)
W/System.err(  707):    at com.baidu.lbsapi.auth.p.run(Unknown Source:19)
D/TrafficStats(  707): tagSocket(222) with statsTag=0xffffffff, statsUid=-1
I/flutter (  707): 🚀 [定位服务] 定位启动结果: true
I/flutter (  707): 📱 [签到页面] 定位刷新请求已发送
D/nativeloader(  707): Load /data/app/~~VwHbbpcXmBKwwa83qynoeg==/com.yishuo.internship-3s5nMyO4imZYA3lMnfvjkA==/lib/arm64/liblocSDK8b.so using ns clns-7 from class loader (caller=/data/app/~~VwHbbpcXmBKwwa83qynoeg==/com.yishuo.internship-3s5nMyO4imZYA3lMnfvjkA==/base.apk): ok
E/LB      (  707): fail to open node: No such file or directory
D/TrafficStats(  707): tagSocket(240) with statsTag=0xffffffff, statsUid=-1
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
D/AppOpsManager(  707): Noted app header size: 36
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
D/TrafficStats(  707): tagSocket(225) with statsTag=0xffffffff, statsUid=-1
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
I/flutter (  707): 📍 [定位服务] 收到定位结果
I/flutter (  707): 📍 [定位服务] 经度: 114.511719, 纬度: 30.557378
I/flutter (  707): 📍 [定位服务] 地址: 中国湖北省武汉市洪山区花山街道花城南路
I/flutter (  707): ✅ [定位服务] 定位成功: 中国湖北省武汉市洪山区花山街道花城南路
I/flutter (  707): 📱 [签到页面] 收到定位状态变化: LocationStatus.success, 位置: 中国湖北省武汉市洪山区花山街道花城南路
W/1.raster(  707): type=1400 audit(0.0:175964): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
W/1.raster(  707): type=1400 audit(0.0:175965): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
I/shuo.internship(  707): ProcessProfilingInfo new_methods=42 is saved saved_to_disk=1 resolve_classes_delay=8000
W/1.raster(  707): type=1400 audit(0.0:175967): avc:  denied  { getattr } for  path="/sys/module/metis/parameters/minor_window_app" dev="sysfs" ino=71007 scontext=u:r:untrusted_app:s0:c161,c257,c512,c768 tcontext=u:object_r:sysfs_migt:s0 tclass=file permissive=0 app=com.yishuo.internship
E/LB      (  707): fail to open node: No such file or directory
E/AndroidRuntime(16091): FATAL EXCEPTION: Thread-6
E/AndroidRuntime(16091): Process: com.yishuo.internship:remote, PID: 16091
E/AndroidRuntime(16091): java.lang.ExceptionInInitializerError
E/AndroidRuntime(16091):        at okhttp3.OkHttpClient.newSslSocketFactory(OkHttpClient.java:296)
E/AndroidRuntime(16091):        at okhttp3.OkHttpClient.<init>(OkHttpClient.java:262)
E/AndroidRuntime(16091):        at okhttp3.OkHttpClient$Builder.build(OkHttpClient.java:1054)
E/AndroidRuntime(16091):        at com.baidu.location.b.r.b(Unknown Source:42)
E/AndroidRuntime(16091):        at com.baidu.location.b.r.<init>(Unknown Source:6)
E/AndroidRuntime(16091):        at com.baidu.location.b.r$b.<clinit>(Unknown Source:2)
E/AndroidRuntime(16091):        at com.baidu.location.b.r.a(Unknown Source:0)
E/AndroidRuntime(16091):        at com.baidu.location.h.g.a(Unknown Source:18)
E/AndroidRuntime(16091):        at com.baidu.location.h.g.a(Unknown Source:0)
E/AndroidRuntime(16091):        at com.baidu.location.h.g$7.run(Unknown Source:4)
E/AndroidRuntime(16091):        at java.lang.Thread.run(Thread.java:1012)
E/AndroidRuntime(16091): Caused by: java.lang.IllegalStateException: Expected Android API level 21+ but was 35
E/AndroidRuntime(16091):        at okhttp3.internal.platform.AndroidPlatform.buildIfSupported(AndroidPlatform.java:238)
E/AndroidRuntime(16091):        at okhttp3.internal.platform.Platform.findPlatform(Platform.java:202)
E/AndroidRuntime(16091):        at okhttp3.internal.platform.Platform.<clinit>(Platform.java:79)
E/AndroidRuntime(16091):        ... 11 more
I/MIUIInput(  707): [MotionEvent] ViewRootImpl windowName 'com.yishuo.internship/com.yishuo.internship.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=130876920, downTime=130876920, phoneEventTime=10:59:46.644 } moveCount:0
I/MIUIInput(  707): [MotionEvent] ViewRootImpl windowName 'com.yishuo.internship/com.yishuo.internship.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=130877008, downTime=130876920, phoneEventTime=10:59:46.733 } moveCount:0
I/flutter (  707): 🗺️ [位置调整] 初始化位置: LocationDataModel(latitude: 30.557378, longitude: 114.511719, address: 中国湖北省武汉市洪山区花山街道花  城南路, province: 湖北省, city: 武汉市)
I/flutter (  707): 🔍 [搜索] 初始化百度地图搜索
I/flutter (  707): ✅ [搜索] 百度地图搜索初始化完成
I/flutter (  707): 🏠 [附近地址] 开始加载附近地址
I/flutter (  707): 🏠 [附近地址] 当前位置: 30.557378, 114.511719
I/flutter (  707): didChangeDependencies
E/LB      (  707): fail to open node: No such file or directory
I/flutter (  707): 🏠 [附近地址] 搜索请求发送结果: true
D/TrafficStats(  707): tagSocket(166) with statsTag=0xffffffff, statsUid=-1
D/nativeloader(  707): Load /data/app/~~VwHbbpcXmBKwwa83qynoeg==/com.yishuo.internship-3s5nMyO4imZYA3lMnfvjkA==/lib/arm64/libBaiduMapSDK_map_v7_6_4.so using ns clns-7 from class loader (caller=/data/app/~~VwHbbpcXmBKwwa83qynoeg==/com.yishuo.internship-3s5nMyO4imZYA3lMnfvjkA==/base.apk!classes6.dex): ok
W/shuo.internship(  707): CheckJNI: method to register "nativeSetLayerTag" not in the given class. This is slow, consider changing your RegisterNatives calls.
D/DEBUG   (  707): ThreadProc, CVSocketMan::SocketThreadProc start ...
I/GLThread(  707): noticed surfaceView surface lost tid=17157
D/CompatChangeReporter(  707): Compat change id reported: 63938206; UID 10417; state: ENABLED
I/shuo.internship(  707): Compiler allocated 4215KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
I/GLThread(  707): onResume tid=17157
I/PlatformViewsController(  707): Using hybrid composition for platform view: 0
I/flutter (  707): 🗺️ [位置调整] 地图初始化完成
I/flutter (  707): 🗺️ [位置调整] 添加位置标记: 30.557378, 114.511719
W/libc    (  707): Access denied finding property "vendor.gpp.create_frc_extension"
E/qdgralloc(  707): GetGpuPixelFormat: No map for format: 0x3b
E/AdrenoUtils(  707): <validate_memory_layout_input_params:1805>: Unknown Format 0
E/AdrenoUtils(  707): <adreno_init_memory_layout:4055>: Memory Layout input parameter validation failed!
W/qdgralloc(  707): GetGpuResourceSizeAndDimensions Graphics metadata init failed
E/Gralloc4(  707): isSupported(1, 1, 59, 1, ...) failed with 7
I/AHardwareBuffer(  707): proc name :com.yishuo.internship
E/GraphicBufferAllocator(  707): Failed to allocate (4 x 4) layerCount 1 format 59 usage 300: 2
E/AHardwareBuffer(  707): GraphicBuffer(w=4, h=4, lc=1) failed (Unknown error -2), handle=0x0
E/qdgralloc(  707): GetGpuPixelFormat: No map for format: 0x3b
E/AdrenoUtils(  707): <validate_memory_layout_input_params:1805>: Unknown Format 0
E/AdrenoUtils(  707): <adreno_init_memory_layout:4055>: Memory Layout input parameter validation failed!
W/qdgralloc(  707): GetGpuResourceSizeAndDimensions Graphics metadata init failed
E/Gralloc4(  707): isSupported(1, 1, 59, 1, ...) failed with 7
I/AHardwareBuffer(  707): proc name :com.yishuo.internship
E/GraphicBufferAllocator(  707): Failed to allocate (4 x 4) layerCount 1 format 59 usage 300: 2
E/AHardwareBuffer(  707): GraphicBuffer(w=4, h=4, lc=1) failed (Unknown error -2), handle=0x0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
D/GLTextureView(  707): onAttachedToWindow reattach =false
E/LB      (  707): fail to open node: No such file or directory
W/libc    (  707): Access denied finding property "vendor.gpp.create_frc_extension"
E/qdgralloc(  707): GetGpuPixelFormat: No map for format: 0x3b
E/AdrenoUtils(  707): <validate_memory_layout_input_params:1805>: Unknown Format 0
E/AdrenoUtils(  707): <adreno_init_memory_layout:4055>: Memory Layout input parameter validation failed!
W/qdgralloc(  707): GetGpuResourceSizeAndDimensions Graphics metadata init failed
E/Gralloc4(  707): isSupported(1, 1, 59, 1, ...) failed with 7
I/AHardwareBuffer(  707): proc name :com.yishuo.internship
E/GraphicBufferAllocator(  707): Failed to allocate (4 x 4) layerCount 1 format 59 usage 300: 2
E/AHardwareBuffer(  707): GraphicBuffer(w=4, h=4, lc=1) failed (Unknown error -2), handle=0x0
E/qdgralloc(  707): GetGpuPixelFormat: No map for format: 0x3b
E/AdrenoUtils(  707): <validate_memory_layout_input_params:1805>: Unknown Format 0
E/AdrenoUtils(  707): <adreno_init_memory_layout:4055>: Memory Layout input parameter validation failed!
W/qdgralloc(  707): GetGpuResourceSizeAndDimensions Graphics metadata init failed
E/Gralloc4(  707): isSupported(1, 1, 59, 1, ...) failed with 7
I/AHardwareBuffer(  707): proc name :com.yishuo.internship
E/GraphicBufferAllocator(  707): Failed to allocate (4 x 4) layerCount 1 format 59 usage 300: 2
E/AHardwareBuffer(  707): GraphicBuffer(w=4, h=4, lc=1) failed (Unknown error -2), handle=0x0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
E/LB      (  707): fail to open node: No such file or directory
D/NetworkLogic(  707): onNetWorkChanged-0
D/NetworkLogic(  707): NetworkDetect
D/NetworkLogic(  707): onNetWorkChanged-CELLULAR
E/LB      (  707): fail to open node: No such file or directory
E/LB      (  707): fail to open node: No such file or directory
D/DEBUG   (  707): CNetworkDetectEngine::NetworkDetect Start 1
D/NetworkLogic(  707): onNetWorkChanged-CELLULAR
W/libc    (  707): Access denied finding property "vendor.gpp.create_frc_extension"
I/GLThread(  707): noticed surfaceView surface acquired tid=17157
W/EglHelper(  707): start() tid=17157
W/EglHelper(  707): createContext com.google.android.gles_jni.EGLContextImpl@b489e69f tid=17157
I/GLThread(  707): noticing that we want render notification tid=17157
I/Main thread(  707): onWindowResize waiting for render complete from tid=17157
W/GLThread(  707): egl createSurface
W/EglHelper(  707): createSurface()  tid=17157
W/libc    (  707): Access denied finding property "vendor.gpp.create_frc_extension"
W/GLThreadManager(  707): checkGLESVersion mGLESVersion = 196610 mMultipleGLESContextsAllowed = true
W/GLThreadManager(  707): checkGLDriver renderer = "Adreno (TM) 750" multipleContextsAllowed = true mLimitedGLESContexts = false
W/GLThread(  707): onSurfaceCreated
W/GLThread(  707): onSurfaceChanged(1440, 959)
I/Main thread(  707): onWindowResize waiting for render complete from tid=17157
I/GLThread(  707): sending render notification tid=17157
I/GLThread(  707): noticing that we want render notification tid=17157
W/GLThread(  707): egl createSurface
W/EglHelper(  707): createSurface()  tid=17157
W/libc    (  707): Access denied finding property "vendor.gpp.create_frc_extension"
W/GLThread(  707): onSurfaceChanged(1440, 959)
I/GLThread(  707): sending render notification tid=17157
I/flutter (  707): 🏠 [附近地址] 收到搜索结果，错误码: BMFSearchErrorCode.NO_ERROR
I/flutter (  707): ✅ [附近地址] 搜索成功，结果数量: 3
I/flutter (  707): 🏠 [附近地址] POI 0: 武汉市光谷第二十六小学, 原始距离: 0m
I/flutter (  707): 🏠 [附近地址] POI 0 计算距离: 718m
I/flutter (  707): 🏠 [附近地址] POI 1: 华中科技大学附属花城小学-南门, 原始距离: 0m
I/flutter (  707): 🗺️ [地图移动] 地图区域变化结束，原因: BMFRegionChangeReason.APIs
I/flutter (  707): 🏠 [附近地址] POI 1 计算距离: 949m
I/flutter (  707): 🏠 [附近地址] POI 2: 武汉市光谷第二十六小学-南1门, 原始距离: 0m
I/flutter (  707): 🏠 [附近地址] POI 2 计算距离: 633m
I/flutter (  707): ✅ [附近地址] 附近地址更新完成
I/flutter (  707): didUpdateWidget
I/shuo.internship(  707): This is non sticky GC, maxfree is 33554432 minfree is 8388608
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /product/app/ContentCatcherOS2/ContentCatcherOS2.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /system_ext/priv-app/RtMiCloudSDK/RtMiCloudSDK.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /system_ext/app/miuisystem/miuisystem.apk' with 1 weak references     
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~quXACfzOm8Kp209pjAGXHA==/com.google.android.marvin.talkback-tsOq_a_1O-IBVTwZiIDtqQ==/base.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~quXACfzOm8Kp209pjAGXHA==/com.google.android.marvin.talkback-tsOq_a_1O-IBVTwZiIDtqQ==/split_config.arm64_v8a.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~EhzizzBTJtvrQZYG80K6eA==/com.UCMobile-wSDNuBNcwiqwSp4oyCgm_g==/base.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~u5Ghv5VWMzxL06WtYsx4ww==/com.aliyun.tongyi-7Lr_vnqAMQitXacw1jjPuw==/base.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~RZvaneUpJwIUJJ3ILSi8kQ==/com.qiyi.video-frc-uJeSLfMsgOXZKCztNQ==/base.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~L624PejfXEUBNzIbpAzZeQ==/com.ss.android.article.news-u5tReBL6HJbieK4MsoaXCA==/base.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~Pr88J3CNWDw-jFxLsXPt5A==/io.legado.app.release-ux8Wdcs3eAiPSkptrcCrGw==/base.apk' with 1 weak references
W/shuo.internship(  707): ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~HDevKELtGZHu1SsLCXT9OA==/mark.via-SNrzl2YwVbiypt851IcROg==/base.apk' with 1 weak references
I/MIUIInput(  707): [MotionEvent] ViewRootImpl windowName 'com.yishuo.internship/com.yishuo.internship.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=130879645, downTime=130879645, phoneEventTime=10:59:49.369 } moveCount:0
I/MIUIInput(  707): [MotionEvent] ViewRootImpl windowName 'com.yishuo.internship/com.yishuo.internship.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=130880164, downTime=130879645, phoneEventTime=10:59:49.888 } moveCount:25
I/flutter (  707): 🗺️ [地图移动] 地图区域变化结束，原因: BMFRegionChangeReason.Gesture
I/flutter (  707): 🗺️ [地图移动] 新的中心点: 30.55819332166596, 114.51314850512162
I/shuo.internship(  707): This is non sticky GC, maxfree is 33554432 minfree is 8388608
I/flutter (  707): 🏠 [地图移动] 开始加载新位置的附近地址: 30.55819332166596, 114.51314850512162
I/flutter (  707): 🏠 [地图移动] 搜索请求发送结果: true
D/TrafficStats(  707): tagSocket(294) with statsTag=0xffffffff, statsUid=-1
I/flutter (  707): didUpdateWidget
I/shuo.internship(  707): This is non sticky GC, maxfree is 33554432 minfree is 8388608
I/shuo.internship(  707): This is non sticky GC, maxfree is 33554432 minfree is 8388608
I/shuo.internship(  707): This is non sticky GC, maxfree is 33554432 minfree is 8388608
I/flutter (  707): 🏠 [附近地址] 收到搜索结果，错误码: BMFSearchErrorCode.NO_ERROR
I/flutter (  707): ✅ [附近地址] 搜索成功，结果数量: 3
I/flutter (  707): 🏠 [附近地址] POI 0: 武汉市光谷第二十六小学, 原始距离: 0m
I/flutter (  707): 🏠 [附近地址] POI 0 计算距离: 842m
I/flutter (  707): 🏠 [附近地址] POI 1: 华中科技大学附属花城小学-南门, 原始距离: 0m
I/flutter (  707): 🏠 [附近地址] POI 1 计算距离: 948m
I/flutter (  707): 🏠 [附近地址] POI 2: 武汉市光谷第二十六小学-南1门, 原始距离: 0m
I/flutter (  707): 🏠 [附近地址] POI 2 计算距离: 759m
I/flutter (  707): ✅ [附近地址] 附近地址更新完成
I/flutter (  707): didUpdateWidget
I/shuo.internship(  707): This is non sticky GC, maxfree is 33554432 minfree is 8388608
I/MIUIInput(  707): [KeyEvent] ViewRootImpl windowName 'sendBackKeyEvent-com.yishuo.internship/com.yishuo.internship.MainActivity', KeyEvent { action=ACTION_DOWN, keyCode=KEYCODE_BACK, scanCode=0, metaState=0, flags=0x48, repeatCount=0, eventTime=130885886000000, downTime=130885886000000, deviceId=-1, source=0x101, displayId=-1 }, phoneEventTime=10:59:55.611
I/MIUIInput(  707): [KeyEvent] ViewRootImpl windowName 'sendBackKeyEvent-com.yishuo.internship/com.yishuo.internship.MainActivity', KeyEvent { action=ACTION_UP, keyCode=KEYCODE_BACK, scanCode=0, metaState=0, flags=0x48, repeatCount=0, eventTime=130885896000000, downTime=130885896000000, deviceId=-1, source=0x101, displayId=-1 }, phoneEventTime=10:59:55.620
W/MIUIInput(  707): Back key is intercepted by the app
I/flutter (  707): didUpdateWidget
W/WindowOnBackDispatcher(  707): OnBackInvokedCallback is not enabled for the application.
W/WindowOnBackDispatcher(  707): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
I/shuo.internship(  707): This is non sticky GC, maxfree is 33554432 minfree is 8388608
I/shuo.internship(  707): This is non sticky GC, maxfree is 33554432 minfree is 8388608
I/flutter (  707): implement dispose
D/GLTextureView(  707): onDetachedFromWindow
W/EglHelper(  707): destroySurface()  tid=17157
W/EglHelper(  707): finish() tid=17157
W/qdgralloc(  707): GetSize: Pixel format: 0x3b is not supported by gralloc
W/qdgralloc(  707): gralloc failed to allocate buffer for size 0 format 59 AWxAH 1x1 usage 2816
E/Gralloc4(  707): isSupported(1, 1, 59, 1, ...) failed with 7
I/AHardwareBuffer(  707): proc name :com.yishuo.internship
E/GraphicBufferAllocator(  707): Failed to allocate (4 x 4) layerCount 1 format 59 usage b00: 2
E/AHardwareBuffer(  707): GraphicBuffer(w=4, h=4, lc=1) failed (Unknown error -2), handle=0x0
W/qdgralloc(  707): GetSize: Pixel format: 0x3b is not supported by gralloc
W/qdgralloc(  707): gralloc failed to allocate buffer for size 0 format 59 AWxAH 1x1 usage 2816
E/Gralloc4(  707): isSupported(1, 1, 59, 1, ...) failed with 7
I/AHardwareBuffer(  707): proc name :com.yishuo.internship
E/GraphicBufferAllocator(  707): Failed to allocate (4 x 4) layerCount 1 format 59 usage b00: 2
E/AHardwareBuffer(  707): GraphicBuffer(w=4, h=4, lc=1) failed (Unknown error -2), handle=0x0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
W/qdgralloc(  707): getInterlacedFlag: getMetaData returned 3, defaulting to interlaced_flag = 0
I/MIUIInput(  707): [MotionEvent] ViewRootImpl windowName 'com.yishuo.internship/com.yishuo.internship.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=130888681, downTime=130888681, phoneEventTime=10:59:58.405 } moveCount:0
I/MIUIInput(  707): [MotionEvent] ViewRootImpl windowName 'com.yishuo.internship/com.yishuo.internship.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=130888689, downTime=130888681, phoneEventTime=10:59:58.413 } moveCount:0
I/flutter (  707): 📱 [签到页面] dispose 开始...
I/flutter (  707): 📱 [签到页面] 保持定位服务运行，供其他页面使用
I/flutter (  707): 📱 [签到页面] dispose 完成
I/MIUIInput(  707): [MotionEvent] ViewRootImpl windowName 'com.yishuo.internship/com.yishuo.internship.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=130893181, downTime=130893181, phoneEventTime=11:00:02.905 } moveCount:0
I/MIUIInput(  707): [MotionEvent] ViewRootImpl windowName 'com.yishuo.internship/com.yishuo.internship.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=130893246, downTime=130893181, phoneEventTime=11:00:02.970 } moveCount:0
I/flutter (  707): Button onPressed: 签到
I/flutter (  707): [ERROR] [2025-07-10 11:00:02.984] [_handleFunctionItemTap] 点击功能项: 签到, 路由: /student_sign_in, 用户类型: 1
I/flutter (  707): ModuleNavigationService: 导航到模块 签到, 路由名称: , 路由路径: /student_sign_in, 用户类型: 1
I/flutter (  707): Analytics Event: module_click, Parameters: {title: 签到, route: /student_sign_in, routeName: , userType: 1}
I/flutter (  707): 📱 [签到页面] initState 开始...
I/flutter (  707): 📱 [签到页面] 开始初始化定位服务...
I/flutter (  707): 📱 [签到页面] initState 完成
I/flutter (  707): 🛑 [定位服务] 停止定位...
W/WindowOnBackDispatcher(  707): OnBackInvokedCallback is not enabled for the application.
W/WindowOnBackDispatcher(  707): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
Another exception was thrown: Unable to load asset: "assets/images/laptop_illustration.png".
I/flutter (  707): 📱 [签到页面] 当前定位状态: LocationStatus.success
I/flutter (  707): 📱 [签到页面] 开始刷新定位...
I/flutter (  707): 🔄 [定位服务] 刷新定位... 当前初始化状态: true
I/flutter (  707): 🔐 [定位服务] 检查定位权限...
I/flutter (  707): ✅ [定位服务] 定位权限已授权
I/flutter (  707): 🔄 [定位服务] 服务已初始化，直接开始定位...
I/flutter (  707): 🚀 [定位服务] 开始定位... 初始化状态: true, 插件状态: true
I/flutter (  707): 🚀 [定位服务] 执行定位...
I/flutter (  707): 📡 [定位服务] 设置定位回调...
I/flutter (  707): 🚀 [定位服务] 启动Android定位...
I/flutter (  707): 📱 [签到页面] 收到定位状态变化: LocationStatus.loading, 位置: 正在获取位置信息...
I/flutter (  707): 🚀 [定位服务] 定位启动结果: true
I/flutter (  707): 📱 [签到页面] 定位刷新请求已发送
E/AndroidRuntime(16354): FATAL EXCEPTION: Thread-6
E/AndroidRuntime(16354): Process: com.yishuo.internship:remote, PID: 16354
E/AndroidRuntime(16354): java.lang.ExceptionInInitializerError
E/AndroidRuntime(16354):        at okhttp3.OkHttpClient.newSslSocketFactory(OkHttpClient.java:296)
E/AndroidRuntime(16354):        at okhttp3.OkHttpClient.<init>(OkHttpClient.java:262)
E/AndroidRuntime(16354):        at okhttp3.OkHttpClient$Builder.build(OkHttpClient.java:1054)
E/AndroidRuntime(16354):        at com.baidu.location.b.r.b(Unknown Source:42)
E/AndroidRuntime(16354):        at com.baidu.location.b.r.<init>(Unknown Source:6)
E/AndroidRuntime(16354):        at com.baidu.location.b.r$b.<clinit>(Unknown Source:2)
E/AndroidRuntime(16354):        at com.baidu.location.b.r.a(Unknown Source:0)
E/AndroidRuntime(16354):        at com.baidu.location.h.g.a(Unknown Source:18)
E/AndroidRuntime(16354):        at com.baidu.location.h.g.a(Unknown Source:0)
E/AndroidRuntime(16354):        at com.baidu.location.h.g$7.run(Unknown Source:4)
E/AndroidRuntime(16354):        at java.lang.Thread.run(Thread.java:1012)
E/AndroidRuntime(16354): Caused by: java.lang.IllegalStateException: Expected Android API level 21+ but was 35
E/AndroidRuntime(16354):        at okhttp3.internal.platform.AndroidPlatform.buildIfSupported(AndroidPlatform.java:238)
E/AndroidRuntime(16354):        at okhttp3.internal.platform.Platform.findPlatform(Platform.java:202)
E/AndroidRuntime(16354):        at okhttp3.internal.platform.Platform.<clinit>(Platform.java:79)
E/AndroidRuntime(16354):        ... 11 more
I/flutter (  707): ⏰ [定位服务] 定位超时，重新尝试...
I/flutter (  707): 📱 [签到页面] 收到定位状态变化: LocationStatus.error, 位置: 定位超时
I/ContentCatcher(  707): SettingTrigger : unregister status observer com.yishuo.internship.MainActivity
D/VRI[MainActivity](  707): visibilityChanged oldVisibility=true newVisibility=false
Lost connection to device.


