/// -----
/// sign_in_record_model.dart
///
/// 签到记录数据模型
/// 对应API返回的单条签到记录数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 签到记录数据模型
///
/// 对应API返回的单条签到记录数据
class SignInRecordModel extends Equatable {
  /// 主键ID
  final String id;
  
  /// 学生ID
  final String studentId;
  
  /// 学生姓名
  final String studentName;
  
  /// 关联实习计划ID
  final String planId;
  
  /// 查询年
  final int year;
  
  /// 查询月份
  final int month;
  
  /// 查询日期
  final int day;
  
  /// 签到时间戳（毫秒）
  final int signTime;
  
  /// 签到纬度
  final String latitude;
  
  /// 签到经度
  final String longitude;
  
  /// 签到省
  final String province;
  
  /// 签到市
  final String city;
  
  /// 签到位置描述
  final String location;
  
  /// 签到设备信息（如手机型号、操作系统）
  final String deviceInfo;
  
  /// 签到附件
  final String? fileUrl;
  
  /// 签到状态（1=正常，2=跨省，3=跨市）
  final int status;
  
  /// 创建时间戳
  final int createTime;
  
  /// 更新时间戳
  final int? updateTime;

  const SignInRecordModel({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.planId,
    required this.year,
    required this.month,
    required this.day,
    required this.signTime,
    required this.latitude,
    required this.longitude,
    required this.province,
    required this.city,
    required this.location,
    required this.deviceInfo,
    this.fileUrl,
    required this.status,
    required this.createTime,
    this.updateTime,
  });

  /// 从JSON创建实例
  factory SignInRecordModel.fromJson(Map<String, dynamic> json) {
    return SignInRecordModel(
      id: json['id'] as String,
      studentId: json['studentId'] as String,
      studentName: json['studentName'] as String,
      planId: json['planId'] as String,
      year: json['year'] as int,
      month: json['month'] as int,
      day: json['day'] as int,
      signTime: json['signTime'] as int,
      latitude: json['latitude'] as String,
      longitude: json['longitude'] as String,
      province: json['province'] as String,
      city: json['city'] as String,
      location: json['location'] as String,
      deviceInfo: json['deviceInfo'] as String,
      fileUrl: json['fileUrl'] as String?,
      status: json['status'] as int,
      createTime: json['createTime'] as int,
      updateTime: json['updateTime'] as int?,
    );
  }

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentId': studentId,
      'studentName': studentName,
      'planId': planId,
      'year': year,
      'month': month,
      'day': day,
      'signTime': signTime,
      'latitude': latitude,
      'longitude': longitude,
      'province': province,
      'city': city,
      'location': location,
      'deviceInfo': deviceInfo,
      'fileUrl': fileUrl,
      'status': status,
      'createTime': createTime,
      'updateTime': updateTime,
    };
  }

  /// 复制并更新部分属性
  SignInRecordModel copyWith({
    String? id,
    String? studentId,
    String? studentName,
    String? planId,
    int? year,
    int? month,
    int? day,
    int? signTime,
    String? latitude,
    String? longitude,
    String? province,
    String? city,
    String? location,
    String? deviceInfo,
    String? fileUrl,
    int? status,
    int? createTime,
    int? updateTime,
  }) {
    return SignInRecordModel(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      planId: planId ?? this.planId,
      year: year ?? this.year,
      month: month ?? this.month,
      day: day ?? this.day,
      signTime: signTime ?? this.signTime,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      province: province ?? this.province,
      city: city ?? this.city,
      location: location ?? this.location,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      fileUrl: fileUrl ?? this.fileUrl,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  List<Object?> get props => [
    id,
    studentId,
    studentName,
    planId,
    year,
    month,
    day,
    signTime,
    latitude,
    longitude,
    province,
    city,
    location,
    deviceInfo,
    fileUrl,
    status,
    createTime,
    updateTime,
  ];

  @override
  String toString() {
    return 'SignInRecordModel(id: $id, studentId: $studentId, studentName: $studentName, planId: $planId, year: $year, month: $month, day: $day, signTime: $signTime, latitude: $latitude, longitude: $longitude, province: $province, city: $city, location: $location, deviceInfo: $deviceInfo, fileUrl: $fileUrl, status: $status, createTime: $createTime, updateTime: $updateTime)';
  }
}
