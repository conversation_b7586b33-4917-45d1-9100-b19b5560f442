/// -----
/// user_info_remote_data_source_impl.dart
/// 
/// 用户信息远程数据源实现，调用用户中心API获取用户信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/profile/data/datasources/remote/user_info_remote_data_source.dart';
import 'package:flutter_demo/features/profile/data/models/user_info_model.dart';

/// 用户信息远程数据源实现
///
/// 实现从远程API获取用户信息的具体逻辑
/// 使用DioClient进行网络请求
class UserInfoRemoteDataSourceImpl implements UserInfoRemoteDataSource {
  final DioClient _dioClient;
  
  static const String _tag = 'UserInfoRemoteDataSource';

  UserInfoRemoteDataSourceImpl(this._dioClient);

  @override
  Future<UserInfoModel> getUserInfo() async {
    try {
      Logger.info(_tag, '开始获取用户信息');
      
      // 调用用户中心接口
      final response = await _dioClient.get(
        'userservice/v1/user/center/userInfo',
      );

      Logger.info(_tag, '用户信息获取成功');
      
      // 解析响应数据
      if (response != null) {
        // DioClient已经提取了data字段，直接使用response数据
        final userInfo = UserInfoModel.fromJson(response);
        
        Logger.debug(_tag, '用户信息解析成功: ${userInfo.userName}');
        return userInfo;
      } else {
        Logger.error(_tag, '用户信息响应数据为空');
        throw ServerException('用户信息响应数据为空');
      }
    } catch (e) {
      Logger.error(_tag, '获取用户信息失败: $e');
      
      // 如果是已知的异常类型，直接重新抛出
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      
      // 其他异常转换为ServerException
      throw ServerException('获取用户信息失败: $e');
    }
  }

  @override
  Future<bool> updateUserGender(int gender) async {
    try {
      Logger.info(_tag, '开始更新用户性别: $gender');

      // 调用更新性别接口
      final response = await _dioClient.post(
        'userservice/v1/user/center/saveGender',
        data: {
          'gender': gender,
        },
      );

      Logger.info(_tag, '用户性别更新成功');

      // 检查响应是否成功
      // DioClient已经处理了resultCode验证，如果到这里说明请求成功
      return true;
    } catch (e) {
      Logger.error(_tag, '更新用户性别失败: $e');

      // 如果是已知的异常类型，直接重新抛出
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }

      // 其他异常转换为ServerException
      throw ServerException('更新用户性别失败: $e');
    }
  }

  @override
  Future<bool> saveUserAvatar(String avatar) async {
    try {
      Logger.info(_tag, '开始保存用户头像: $avatar');

      // 调用保存头像接口
      await _dioClient.post(
        'userservice/v1/user/center/saveAvatar',
        data: {
          'avatar': avatar,
        },
      );

      Logger.info(_tag, '用户头像保存成功');

      // 检查响应是否成功
      // DioClient已经处理了resultCode验证，如果到这里说明请求成功
      return true;
    } catch (e) {
      Logger.error(_tag, '保存用户头像失败: $e');

      // 如果是已知的异常类型，直接重新抛出
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }

      // 其他异常转换为ServerException
      throw ServerException('保存用户头像失败: $e');
    }
  }

  @override
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    try {
      Logger.info(_tag, '开始修改用户密码');

      // 调用修改密码接口
      await _dioClient.post(
        'userservice/v1/user/resetPassword',
        data: {
          'password': oldPassword,
          'newPassword': newPassword,
        },
      );

      Logger.info(_tag, '用户密码修改成功');

      // 检查响应是否成功
      // DioClient已经处理了resultCode验证，如果到这里说明请求成功
      return true;
    } catch (e) {
      Logger.error(_tag, '修改用户密码失败: $e');

      // 如果是已知的异常类型，直接重新抛出
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }

      // 其他异常转换为ServerException
      throw ServerException('修改用户密码失败: $e');
    }
  }
}
