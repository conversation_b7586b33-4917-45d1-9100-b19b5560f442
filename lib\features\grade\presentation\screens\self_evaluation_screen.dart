/// -----
/// self_evaluation_screen.dart
///
/// 自我评分页面，学生对自己的实习表现进行评分
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/screens/score_evaluation_screen.dart';
import 'package:flutter_demo/core/screens/score_evaluation_config_factory.dart';

/// 自我评分页面
///
/// 学生对自己的实习表现进行评分，包括各项评分细节
class SelfEvaluationScreen extends StatelessWidget {
  /// 课程名称
  final String courseName;

  const SelfEvaluationScreen({
    Key? key,
    required this.courseName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 创建自我评分配置
    final config = ScoreEvaluationConfigFactory.createSelfEvaluationConfig(
      courseName: courseName,
      onSubmit: (scores) {
        // 处理自我评分提交逻辑
        debugPrint('自我评分提交: $scores');
        // 这里可以调用API提交评分数据
      },
    );

    // 使用通用评分页面
    return ScoreEvaluationScreen(config: config);
  }
}
