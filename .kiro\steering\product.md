---
inclusion: always
---

# 产品规范 - 亿硕教育实习管理平台

## 业务领域

亿硕教育实习管理平台是一个专为中国教育机构设计的实习管理移动应用，基于Flutter开发。该平台服务于高等教育机构的实习教学管理需求。

## 用户角色与权限

### 学生用户
- 查看个人实习信息和状态
- 提交各类实习报告（日报、周报、月报、总结报告）
- 申请实习变更和岗位调整
- 接受安全教育和查看警示信息
- 使用位置服务进行签到签退

### 教师用户
- 审核学生实习报告
- 管理实习审批流程
- 监控学生实习进度
- 发布安全教育内容
- 查看学生位置和考勤信息

### 管理员用户
- 系统配置和用户管理
- 数据统计和报表生成
- 审批流程配置
- 安全策略管理

## 核心业务流程

### 实习申请流程
1. 学生提交实习申请
2. 多级审批（导师 → 学院 → 学校）
3. 审批结果通知
4. 实习协议签署

### 报告提交周期
- **日报**: 每个工作日提交，记录当日实习活动
- **周报**: 每周提交，总结一周实习收获
- **月报**: 每月提交，深度反思实习体验
- **总结报告**: 实习结束时提交，全面总结实习成果

### 变更申请流程
- 实习单位变更需要重新审批
- 实习岗位调整需要导师确认
- 实习时间延长需要多方协调

## 本地化要求

### 语言规范
- 界面文字必须使用简体中文
- 支持中文输入法优化
- 日期时间格式遵循中国标准
- 数字格式使用中文习惯（如：一万二千三百四十五）

### 文化适应
- 遵循中国教育系统的等级制度
- 体现师生关系的尊重文化
- 符合中国学生的使用习惯
- 考虑中国移动互联网环境特点

## 数据安全与隐私

### 教育数据保护
- 学生个人信息严格保密
- 实习单位信息需要脱敏处理
- 位置数据仅用于考勤，不得滥用
- 报告内容涉及商业机密需要特殊处理

### 合规要求
- 遵循《网络安全法》
- 符合《个人信息保护法》
- 满足教育部相关规定
- 配合学校信息安全政策

## 用户体验原则

### 移动端优先
- 界面设计适配中国主流手机尺寸
- 考虑单手操作便利性
- 优化网络环境较差时的使用体验
- 支持离线功能以应对网络不稳定

### 教育场景适配
- 简化操作流程，降低学习成本
- 提供详细的使用指导
- 考虑不同年龄段学生的接受能力
- 支持批量操作提高效率

## 技术集成要求

### 百度地图集成
- 精确的位置定位服务
- 考勤打卡功能
- 实习单位位置标记
- 路径规划和导航

### 文件系统
- 支持多种文档格式上传
- 图片压缩和优化
- 文件预览功能
- 云存储同步

## 业务规则

### 考勤规则
- 工作日必须打卡签到签退
- 位置偏差不超过500米
- 异常考勤需要说明原因
- 请假需要提前申请

### 报告规则
- 日报字数不少于200字
- 周报需要包含学习总结
- 月报需要导师点评
- 总结报告需要实习单位盖章

### 审批规则
- 普通申请48小时内处理
- 紧急申请24小时内处理
- 拒绝申请必须说明理由
- 审批记录永久保存