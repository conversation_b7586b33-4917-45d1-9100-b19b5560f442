---
inclusion: always
---

# 项目结构与架构规范

## 架构原则

**必须遵循Clean Architecture三层分离**：
- `lib/core/` - 共享基础设施和工具
- `lib/features/` - 按功能模块组织
- 每个功能模块内部严格按照 data → domain → presentation 分层

## 文件放置规则

### 新建功能模块时
```
lib/features/{feature_name}/
├── data/
│   ├── datasources/remote/    # API调用
│   ├── datasources/local/     # 本地缓存
│   ├── models/               # DTO数据模型
│   └── repositories/         # Repository实现
├── domain/
│   ├── entities/            # 业务实体
│   ├── repositories/        # Repository接口
│   └── usecases/           # 业务用例
└── presentation/
    ├── bloc/               # BLoC状态管理
    ├── screens/            # 页面组件
    └── widgets/            # 功能专用组件
```

### 共享组件放置
- 跨功能复用的Widget → `lib/core/widgets/`
- 应用级配置 → `lib/core/config/`
- 全局常量 → `lib/core/constants/`
- 工具函数 → `lib/core/utils/`
- 主题样式 → `lib/core/theme/`

## 命名约定（强制执行）

### 文件命名
- **必须使用snake_case**：`user_profile_screen.dart`
- Screen文件：`{name}_screen.dart`
- Widget文件：`{name}_widget.dart`
- BLoC文件：`{feature}_bloc.dart`, `{feature}_event.dart`, `{feature}_state.dart`

### 类和变量命名
- 类名：PascalCase (`UserProfileScreen`)
- 变量/方法：camelCase (`userName`)
- 常量：SCREAMING_SNAKE_CASE (`API_BASE_URL`)
- 私有成员：下划线前缀 (`_privateMethod`)

## BLoC模式规范

**状态管理必须使用BLoC模式**：
```dart
// 事件定义
abstract class LoginEvent extends Equatable {}
class LoginSubmitted extends LoginEvent {
  final String username, password;
  // 构造函数和props
}

// 状态定义  
abstract class LoginState extends Equatable {}
class LoginInitial extends LoginState {}
class LoginLoading extends LoginState {}
class LoginSuccess extends LoginState {}
class LoginFailure extends LoginState {}

// BLoC实现
class LoginBloc extends Bloc<LoginEvent, LoginState> {
  // 依赖注入和事件处理
}
```

## 导入组织规则

**严格按顺序分组**：
```dart
// 1. Flutter SDK
import 'package:flutter/material.dart';

// 2. 第三方包
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

// 3. 项目内部导入
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/auth/domain/entities/user.dart';
```

## 核心功能模块

### 现有功能模块
- `auth/` - 用户认证（登录、注册、权限）
- `report/` - 报告管理（日报、周报、月报、总结）
- `internship/` - 实习管理（信息、计划、状态）
- `attendance/` - 考勤管理（签到、位置服务、百度地图）
- `approval/` - 审批流程（多级审批、状态跟踪）
- `grade/` - 成绩评价（自评、互评、教师评价）

### 新功能开发规则
1. 先创建domain层（entities, repositories接口, usecases）
2. 再实现data层（models, datasources, repositories实现）
3. 最后开发presentation层（bloc, screens, widgets）
4. 每层都要有对应的测试文件

## 依赖注入规范

- 使用get_it进行服务定位
- 核心服务注册在 `lib/core/di/`
- 功能模块依赖注册在各自的 `di/` 目录
- Repository和UseCase必须通过依赖注入获取

## 测试结构要求

```
test/
├── core/                 # 核心功能测试
├── features/{feature}/
│   ├── data/            # 数据层测试（Repository, DataSource）
│   ├── domain/          # 领域层测试（UseCase, Entity）
│   └── presentation/    # 表现层测试（BLoC, Widget）
```

## 资源文件组织

```
assets/
├── images/              # 图片资源
├── data/               # JSON配置文件
├── html/               # HTML模板
└── pdf/                # PDF文档
```

**重要**：新增资源文件必须在 `pubspec.yaml` 中声明